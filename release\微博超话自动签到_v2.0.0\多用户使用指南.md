# 微博超话自动签到程序 - 多用户功能指南

## 概述

本程序现已支持多用户功能，可以同时管理多个微博账户的超话签到任务。支持用户管理、并发处理、独立配置和详细日志记录。

## 主要功能

### 1. 用户管理系统
- ✅ 添加/删除用户账户
- ✅ 用户状态管理（启用/禁用）
- ✅ 安全的凭据存储（支持加密）
- ✅ 用户统计信息跟踪

### 2. 多用户签到
- ✅ 支持顺序或并发处理
- ✅ 独立的用户配置和超话列表
- ✅ 速率限制防止触发反机器人措施
- ✅ 用户级别的错误隔离

### 3. 配置管理
- ✅ 每个用户独立的超话配置
- ✅ 个性化的签到设置
- ✅ 灵活的调度选项

### 4. 日志和报告
- ✅ 每个用户独立的日志文件
- ✅ 详细的签到结果统计
- ✅ 用户级别的成功率跟踪

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 启动程序

```bash
python main.py --interactive
```

### 3. 添加用户

在交互模式中选择 "1. 添加用户"，然后输入：
- 用户ID（唯一标识符）
- 用户名（显示名称）
- Cookies（从浏览器获取）

### 4. 执行签到

选择 "7. 执行多用户签到" 来为所有启用的用户执行签到。

## 命令行使用

### 用户管理

```bash
# 添加用户
python main.py --add-user user1 "用户1" "your_cookies_here"

# 列出所有用户
python main.py --list-users

# 移除用户
python main.py --remove-user user1

# 启用/禁用用户
python main.py --enable-user user1
python main.py --disable-user user1
```

### 签到操作

```bash
# 执行多用户签到（顺序）
python main.py --multi-checkin

# 执行多用户签到（并发）
python main.py --multi-checkin --concurrent

# 执行指定用户签到
python main.py --user-checkin user1

# 发现用户超话
python main.py --discover-user user1
```

### 系统操作

```bash
# 查看状态
python main.py --status

# 启动定时任务
python main.py --start-scheduler

# 停止定时任务
python main.py --stop-scheduler
```

## 配置文件

### 主配置文件 (config.json)

```json
{
  "multi_user": {
    "enabled": true,
    "concurrent_processing": false,
    "max_concurrent_users": 3,
    "rate_limit_delay": 5
  },
  "logging": {
    "separate_user_logs": true
  }
}
```

### 用户数据文件 (users.json)

用户数据自动管理，支持加密存储。每个用户包含：
- 基本信息（ID、用户名）
- 登录凭据（Cookies）
- 超话配置
- 个人设置
- 状态统计

## 安全特性

### 1. 加密存储
- 用户凭据可选择加密存储
- 使用 PBKDF2 + AES 加密算法
- 自动检测加密库可用性

### 2. 错误隔离
- 单个用户失败不影响其他用户
- 独立的重试机制
- 详细的错误日志

### 3. 速率限制
- 可配置的请求间隔
- 防止触发反机器人检测
- 并发用户数量限制

## 日志系统

### 1. 主日志
- 位置：`logs/weibo_checkin.log`
- 包含：系统级别的操作和统计

### 2. 用户日志
- 位置：`logs/weibo_checkin_user_{user_id}.log`
- 包含：特定用户的详细操作记录

### 3. 日志级别
- INFO：正常操作信息
- WARNING：警告信息
- ERROR：错误信息

## 最佳实践

### 1. 用户管理
- 使用有意义的用户ID和用户名
- 定期更新过期的Cookies
- 及时禁用不需要的用户账户

### 2. 签到策略
- 优先使用顺序处理避免触发限制
- 设置合适的延迟间隔（建议2-5秒）
- 监控成功率，及时调整策略

### 3. 系统维护
- 定期检查日志文件大小
- 备份重要的配置和用户数据
- 监控程序运行状态

## 故障排除

### 1. 用户登录失败
- 检查Cookies是否过期
- 确认账户状态正常
- 查看用户专用日志文件

### 2. 签到失败
- 检查网络连接
- 确认超话ID正确
- 调整请求间隔

### 3. 程序异常
- 查看主日志文件
- 检查配置文件格式
- 确认依赖库安装完整

## 技术支持

如遇到问题，请：
1. 查看相关日志文件
2. 检查配置文件格式
3. 确认网络连接正常
4. 提供详细的错误信息

## 更新日志

### v2.0.0 (多用户版本)
- ✅ 新增多用户管理系统
- ✅ 支持并发签到处理
- ✅ 加密存储用户凭据
- ✅ 独立的用户日志系统
- ✅ 增强的错误处理机制
- ✅ 完善的命令行接口
