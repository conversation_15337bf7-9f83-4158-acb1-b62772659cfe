"""
工具函数模块
"""
import logging
import os
import time
import random
from typing import Optional
from fake_useragent import UserAgent
from bs4 import BeautifulSoup
import re

def setup_logging(config: dict, log_prefix: str = None) -> logging.Logger:
    """
    设置日志配置

    Args:
        config: 日志配置字典
        log_prefix: 日志前缀，用于多用户日志分离

    Returns:
        配置好的logger实例
    """
    log_file = config.get("file", "logs/weibo_checkin.log")
    log_level = config.get("level", "INFO")
    separate_user_logs = config.get("separate_user_logs", True)

    # 如果指定了日志前缀且启用了用户日志分离，修改日志文件路径
    if log_prefix and separate_user_logs:
        log_dir = os.path.dirname(log_file)
        log_name = os.path.basename(log_file)
        name_parts = log_name.rsplit('.', 1)
        if len(name_parts) == 2:
            new_log_name = f"{name_parts[0]}_{log_prefix}.{name_parts[1]}"
        else:
            new_log_name = f"{log_name}_{log_prefix}"
        log_file = os.path.join(log_dir, new_log_name)

    # 创建日志目录
    os.makedirs(os.path.dirname(log_file), exist_ok=True)

    # 配置日志格式
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # 创建logger，使用前缀区分不同的logger
    logger_name = f'weibo_checkin_{log_prefix}' if log_prefix else 'weibo_checkin'
    logger = logging.getLogger(logger_name)
    logger.setLevel(getattr(logging, log_level.upper()))

    # 清除已有的处理器
    logger.handlers.clear()

    # 文件处理器
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)

    # 控制台处理器（只有主logger才输出到控制台，避免重复输出）
    if not log_prefix:
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)

    return logger


def get_random_user_agent() -> str:
    """获取随机User-Agent"""
    try:
        ua = UserAgent()
        return ua.random
    except Exception:
        # 备用User-Agent列表
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        ]
        return random.choice(user_agents)


def random_delay(min_seconds: float = 1.0, max_seconds: float = 3.0) -> None:
    """随机延迟"""
    delay = random.uniform(min_seconds, max_seconds)
    time.sleep(delay)


def safe_get(data: dict, key: str, default=None):
    """安全获取字典值"""
    try:
        keys = key.split('.')
        value = data
        for k in keys:
            value = value[k]
        return value
    except (KeyError, TypeError):
        return default


def format_topic_url(topic_id: str) -> str:
    """格式化超话URL"""
    return f"https://weibo.com/p/{topic_id}/super_index"


def format_checkin_url() -> str:
    """格式化签到URL"""
    return "https://weibo.com/p/aj/general/button"


def parse_topic_id_from_url(url: str) -> Optional[str]:
    """从URL中解析超话ID"""
    import re
    pattern = r'/p/(\d+)'
    match = re.search(pattern, url)
    return match.group(1) if match else None


def mask_sensitive_info(text: str, mask_char: str = '*') -> str:
    """遮蔽敏感信息"""
    if not text or len(text) <= 4:
        return mask_char * len(text) if text else ""
    
    # 保留前2位和后2位，中间用*替代
    return text[:2] + mask_char * (len(text) - 4) + text[-2:]


def validate_topic_id(topic_id: str) -> bool:
    """验证超话ID格式"""
    return topic_id.isdigit() and len(topic_id) >= 10


def get_current_timestamp() -> int:
    """获取当前时间戳（毫秒）"""
    return int(time.time() * 1000)


def format_duration(seconds: float) -> str:
    """格式化时间间隔"""
    if seconds < 60:
        return f"{seconds:.1f}秒"
    elif seconds < 3600:
        return f"{seconds/60:.1f}分钟"
    else:
        return f"{seconds/3600:.1f}小时"
    
def get_checkin_btn(html_content):
    # 使用 BeautifulSoup 解析 HTML
    soup = BeautifulSoup(html_content, 'html.parser')

    # 查找所有 <script> 标签
    script_tags = soup.find_all('script')
    """
    # 调试输出：查看所有 script 标签的位置和内容摘要
    print(f"找到 {len(script_tags)} 个 <script> 标签")
    for i, script in enumerate(script_tags):
        content = script.string or ""
        if len(content) > 50:
            summary = content[:50] + "..."
        else:
            summary = content
        print(f"标签 #{i}: {summary}")
    """
    # 定义更灵活的正则表达式模式
    # 模式1: 宽松匹配 FM.view 函数调用
    pattern1 = re.compile(
        r'FM\.view\(\{(.*)?\}\)',
        re.DOTALL | re.MULTILINE
    )

    # 模式2: 匹配包含特定 ns 属性的对象
    pattern2 = re.compile(
        r'"ns"\s*:\s*"pl\.header\.cardhead\.index"',
        re.DOTALL | re.MULTILINE
    )
    """
    # 遍历所有 script 标签，查找匹配的内容
    matched_script = None
    for script in script_tags:
        content = script.string or ""
        if pattern1.search(content):
            print(content[:50])

    """
    # 遍历所有 script 标签，查找匹配的内容
    matched_script = None
    for script in script_tags:
        content = script.string or ""
        
        # 先检查是否包含 FM.view 调用
        if not pattern1.search(content):
            continue
        
        # 再检查是否包含目标 ns 属性
        if pattern2.search(content):
            matched_script = content
            break

    if matched_script:
        #print("\n找到匹配的 script 内容!")
        
        # 提取 FM.view 函数的参数对象
        fm_view_match = pattern1.search(matched_script)
        if fm_view_match:
            fm_view_args = fm_view_match.group(1)
            #print("\nFM.view 参数对象:")
            #print(fm_view_args.strip()[:50])
            
            # 进一步提取 html 属性值
            html_pattern = re.compile(r'"html"\s*:\s*"(.*)"', re.DOTALL)
            html_match = html_pattern.search(fm_view_args)
            if html_match:
                html_content = html_match.group(1)
                #print("\n提取的 HTML 内容:")
                #print(html_content[:50])  # 显示前200个字符
            else:
                print("\n未找到 HTML 属性")
    else:
        print("\n未找到匹配的 script 内容。请检查页面结构或提供更多信息。")

    #print("==========================================================================")


    def unescape_html(html_str):
        """还原 HTML 转义字符，特别处理结束标签中的反斜杠"""
        # 处理结束标签中的反斜杠 (\/)
        html_str = re.sub(r'<\\/', '</', html_str)
        
        # 处理其他转义字符
        html_str = html_str.replace(r'\"', '"')
        html_str = html_str.replace(r"\\'", "'")
        html_str = html_str.replace(r'\\', '\\')  # 保留普通反斜杠
        
        # 还原特殊空白字符
        html_str = html_str.replace(r'\t', '\t')
        html_str = html_str.replace(r'\r', '\r')
        html_str = html_str.replace(r'\n', '\n')
        
        # 处理 URL 中的双斜杠（保留协议后的双斜杠）
        html_str = re.sub(r'(?<!:)//', '/', html_str)
        
        return html_str

    def format_html(html_str):
        """简单格式化 HTML（添加缩进）"""
        formatted = []
        indent_level = 0
        indent_size = 2
        
        # 分割标签和内容
        tags = re.findall(r'<[^>]*>|[^<]+', html_str)
        
        for tag in tags:
            tag = tag.strip()
            if not tag:
                continue
                
            # 处理开始标签
            if tag.startswith('<') and not tag.startswith('</') and not tag.endswith('/>'):
                formatted.append(' ' * indent_level + tag)
                indent_level += indent_size
            # 处理结束标签
            elif tag.startswith('</'):
                indent_level -= indent_size
                formatted.append(' ' * indent_level + tag)
            # 处理自闭合标签
            elif tag.endswith('/>'):
                formatted.append(' ' * indent_level + tag)
            # 处理内容
            else:
                formatted.append(' ' * indent_level + tag)
        
        return '\n'.join(formatted)

    # 执行转换
    unescaped_html = unescape_html(html_content)
    formatted_html = format_html(unescaped_html)

    """
    # 保存到文件
    with open('restored_html.html', 'w', encoding='utf-8') as f:
        f.write(formatted_html)

    print("HTML 已成功还原并保存到 restored_html.html")
    """
    soup = BeautifulSoup(formatted_html, 'html.parser')
    checkin_btn = soup.find('a', {'action-type': 'widget_take'})
    #print(checkin_btn)

    return checkin_btn


class RetryHelper:
    """重试助手类"""
    
    def __init__(self, max_retries: int = 3, delay: float = 1.0):
        self.max_retries = max_retries
        self.delay = delay
    
    def retry(self, func, *args, **kwargs):
        """执行重试逻辑"""
        last_exception = None
        
        for attempt in range(self.max_retries + 1):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                last_exception = e
                if attempt < self.max_retries:
                    time.sleep(self.delay * (attempt + 1))
                    continue
                else:
                    break
        
        raise last_exception


