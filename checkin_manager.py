"""
签到管理器模块
支持多用户并发签到
"""
import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Dict, List, Optional
from config import Config
from weibo_client import WeiboClient
from utils import setup_logging, format_duration


class CheckinManager:
    """签到管理器类 - 支持多用户"""

    def __init__(self, config: Config):
        self.config = config
        self.logger = setup_logging(config.get_logging_config())
        self.weibo_clients = {}  # 存储每个用户的客户端实例
        self._lock = threading.Lock()  # 线程锁

        # 统一使用多用户架构，不再需要单用户模式的初始化
        # 所有客户端都通过 get_user_client 方法动态创建
    
    def _init_client(self) -> None:
        """初始化微博客户端"""
        user_config = self.config.get_user_config()
        cookies = user_config.get('cookies', {})
        
        if cookies:
            success = self.weibo_client.login_by_cookies(cookies)
            if not success:
                self.logger.warning("Cookies登录失败，请重新设置cookies")
        else:
            self.logger.warning("未设置cookies，请先配置登录信息")
    
    def update_cookies(self, cookies: Dict) -> bool:
        """更新cookies"""
        try:
            success = self.weibo_client.login_by_cookies(cookies)
            if success:
                self.config.update_cookies(cookies)
                self.logger.info("Cookies更新成功")
                return True
            else:
                self.logger.error("Cookies验证失败")
                return False
        except Exception as e:
            self.logger.error(f"更新cookies异常: {e}")
            return False
    
    def discover_topics(self, user_id: str = None) -> List[Dict]:
        """发现用户关注的超话"""
        try:
            # 如果是多用户模式且指定了用户ID，使用指定用户的客户端
            if self.config.is_multi_user_enabled() and user_id:
                client = self.get_user_client(user_id)
                if not client:
                    self.logger.error(f"无法获取用户 {user_id} 的客户端")
                    return []

                self.logger.info(f"开始为用户 {user_id} 发现关注的超话...")
                topics = client.get_user_topics()

                if topics:
                    self.logger.info(f"为用户 {user_id} 发现 {len(topics)} 个超话")

                    # 自动添加到用户配置中
                    for topic in topics:
                        self.config.add_user_topic(user_id, topic['id'], topic['name'])
                    self.logger.info("已自动添加发现的超话到用户配置中")
                else:
                    self.logger.warning(f"用户 {user_id} 未发现任何超话，请检查登录状态")

                return topics

            # 单用户模式
            elif hasattr(self, 'weibo_client'):
                self.logger.info("开始发现用户关注的超话...")
                topics = self.weibo_client.get_user_topics()

                if topics:
                    self.logger.info(f"发现 {len(topics)} 个超话")

                    # 自动添加到配置中
                    checkin_config = self.config.get_checkin_config()
                    if checkin_config.get('auto_discover', True):
                        for topic in topics:
                            self.config.add_topic(topic['id'], topic['name'])
                        self.logger.info("已自动添加发现的超话到配置中")
                else:
                    self.logger.warning("未发现任何超话，请检查登录状态")

                return topics
            else:
                self.logger.error("未初始化微博客户端，请先设置cookies或指定用户ID")
                return []

        except Exception as e:
            self.logger.error(f"发现超话异常: {e}")
            return []
    
    def get_checkin_topics(self) -> List[Dict]:
        """获取需要签到的超话列表"""
        enabled_topics = self.config.get_enabled_topics()
        
        if not enabled_topics:
            self.logger.warning("没有启用的超话，尝试自动发现...")
            self.discover_topics()
            enabled_topics = self.config.get_enabled_topics()
        
        return enabled_topics
    
    def run_checkin(self, user_id: str = None) -> Dict:
        """执行签到任务"""
        start_time = time.time()
        self.logger.info("=" * 50)

        # 如果是多用户模式且指定了用户ID，使用用户签到
        if self.config.is_multi_user_enabled() and user_id:
            self.logger.info(f"开始执行用户 {user_id} 的微博超话签到任务")
            return self.run_user_checkin(user_id)

        # 单用户模式
        self.logger.info("开始执行微博超话签到任务")

        try:
            # 检查是否有微博客户端
            if not hasattr(self, 'weibo_client'):
                result = {
                    'success': False,
                    'message': '未初始化微博客户端，请先设置cookies',
                    'total': 0,
                    'success_count': 0,
                    'failed_count': 0,
                    'already_checked_count': 0,
                    'duration': 0
                }
                self.logger.error(result['message'])
                return result

            # 获取需要签到的超话
            topics = self.get_checkin_topics()

            if not topics:
                result = {
                    'success': False,
                    'message': '没有需要签到的超话',
                    'total': 0,
                    'success_count': 0,
                    'failed_count': 0,
                    'already_checked_count': 0,
                    'duration': 0
                }
                self.logger.warning(result['message'])
                return result

            self.logger.info(f"共有 {len(topics)} 个超话需要签到")

            # 获取签到配置
            checkin_config = self.config.get_checkin_config()
            delay = checkin_config.get('delay_between_checkins', 2)

            # 执行批量签到
            batch_result = self.weibo_client.batch_checkin(topics, delay)

            # 计算耗时
            duration = time.time() - start_time

            # 构造结果
            result = {
                'success': True,
                'message': '签到任务完成',
                'total': batch_result['total'],
                'success_count': batch_result['success'],
                'failed_count': batch_result['failed'],
                'already_checked_count': batch_result['already_checked'],
                'duration': duration,
                'details': batch_result['details']
            }

            # 记录结果
            self._log_checkin_result(result)

            return result

        except Exception as e:
            duration = time.time() - start_time
            result = {
                'success': False,
                'message': f'签到任务异常: {e}',
                'total': 0,
                'success_count': 0,
                'failed_count': 0,
                'already_checked_count': 0,
                'duration': duration
            }
            self.logger.error(result['message'])
            return result

        finally:
            self.logger.info("签到任务结束")
            self.logger.info("=" * 50)
    
    def _log_checkin_result(self, result: Dict) -> None:
        """记录签到结果"""
        total = result['total']
        success = result['success_count']
        failed = result['failed_count']
        already_checked = result['already_checked_count']
        duration = result['duration']
        
        self.logger.info(f"签到结果统计:")
        self.logger.info(f"  总计: {total} 个超话")
        self.logger.info(f"  成功: {success} 个")
        self.logger.info(f"  失败: {failed} 个")
        self.logger.info(f"  已签到: {already_checked} 个")
        self.logger.info(f"  耗时: {format_duration(duration)}")
        
        # 记录失败的详情
        if failed > 0:
            self.logger.warning("失败的超话:")
            for detail in result.get('details', []):
                if not detail['result']['success']:
                    topic_name = detail['topic_name']
                    message = detail['result']['message']
                    self.logger.warning(f"  {topic_name}: {message}")
    
    def check_single_topic(self, topic_id: str, topic_name: str = "", user_id: str = None) -> Dict:
        """检查单个超话签到状态"""
        try:
            # 获取合适的客户端
            if self.config.is_multi_user_enabled() and user_id:
                client = self.get_user_client(user_id)
                if not client:
                    return {'success': False, 'message': f'用户 {user_id} 客户端初始化失败'}
            elif hasattr(self, 'weibo_client'):
                client = self.weibo_client
            else:
                return {'success': False, 'message': '未初始化微博客户端'}

            self.logger.info(f"检查超话 {topic_name}({topic_id}) 的签到状态")
            result = client.check_topic_status(topic_id)

            if result['success']:
                status = "已签到" if result['checked_in'] else "未签到"
                self.logger.info(f"超话 {topic_name} 状态: {status}")
            else:
                self.logger.error(f"检查超话 {topic_name} 状态失败: {result['message']}")

            return result

        except Exception as e:
            self.logger.error(f"检查超话状态异常: {e}")
            return {'success': False, 'message': f'检查异常: {e}'}

    def checkin_single_topic(self, topic_id: str, topic_name: str = "", user_id: str = None) -> Dict:
        """签到单个超话"""
        try:
            # 获取合适的客户端
            if self.config.is_multi_user_enabled() and user_id:
                client = self.get_user_client(user_id)
                if not client:
                    return {'success': False, 'message': f'用户 {user_id} 客户端初始化失败'}
            elif hasattr(self, 'weibo_client'):
                client = self.weibo_client
            else:
                return {'success': False, 'message': '未初始化微博客户端'}

            self.logger.info(f"开始签到超话 {topic_name}({topic_id})")
            result = client.checkin_topic(topic_id, topic_name)

            if result['success']:
                if result.get('already_checked'):
                    self.logger.info(f"超话 {topic_name} 今日已签到")
                else:
                    self.logger.info(f"超话 {topic_name} 签到成功")
            else:
                self.logger.error(f"超话 {topic_name} 签到失败: {result['message']}")

            return result

        except Exception as e:
            self.logger.error(f"签到超话异常: {e}")
            return {'success': False, 'message': f'签到异常: {e}'}
    
    def add_topic(self, topic_id: str, topic_name: str) -> bool:
        """添加超话到签到列表"""
        try:
            self.config.add_topic(topic_id, topic_name)
            self.logger.info(f"已添加超话: {topic_name}({topic_id})")
            return True
        except Exception as e:
            self.logger.error(f"添加超话失败: {e}")
            return False
    
    def remove_topic(self, topic_id: str) -> bool:
        """从签到列表移除超话"""
        try:
            self.config.remove_topic(topic_id)
            self.logger.info(f"已移除超话: {topic_id}")
            return True
        except Exception as e:
            self.logger.error(f"移除超话失败: {e}")
            return False
    
    def list_topics(self) -> List[Dict]:
        """列出所有配置的超话"""
        return self.config.get_enabled_topics()

    # ========== 多用户支持方法 ==========

    def get_user_client(self, user_id: str) -> Optional[WeiboClient]:
        """
        获取用户的微博客户端实例

        Args:
            user_id: 用户ID

        Returns:
            WeiboClient实例，如果用户不存在返回None
        """
        with self._lock:
            if user_id not in self.weibo_clients:
                user_data = self.config.get_user(user_id)
                if not user_data:
                    return None

                # 创建用户专用的日志记录器
                user_logger = setup_logging(
                    self.config.get_logging_config(),
                    log_prefix=f"user_{user_id}"
                )

                # 创建客户端实例，传递用户ID
                client = WeiboClient(user_logger, user_id)
                cookies = user_data.get('cookies', {})

                if cookies:
                    success = client.login_by_cookies(cookies)
                    if not success:
                        user_logger.warning(f"用户 {user_id} Cookies登录失败")
                        return None
                else:
                    user_logger.warning(f"用户 {user_id} 未设置cookies")
                    return None

                self.weibo_clients[user_id] = client

            return self.weibo_clients[user_id]

    def run_multi_user_checkin(self, user_ids: List[str] = None, concurrent: bool = None) -> Dict:
        """
        执行多用户签到任务

        Args:
            user_ids: 要执行签到的用户ID列表，如果为None则处理所有启用的用户
            concurrent: 是否并发处理，如果为None则使用配置中的设置

        Returns:
            签到结果字典
        """
        start_time = time.time()
        self.logger.info("=" * 50)
        self.logger.info("开始执行多用户微博超话签到任务")

        try:
            # 获取要处理的用户列表
            if user_ids is None:
                enabled_users = self.config.get_enabled_users()
                user_ids = list(enabled_users.keys())

            if not user_ids:
                result = {
                    'success': False,
                    'message': '没有需要处理的用户',
                    'total_users': 0,
                    'success_users': 0,
                    'failed_users': 0,
                    'user_results': {},
                    'duration': 0
                }
                self.logger.warning(result['message'])
                return result

            self.logger.info(f"共有 {len(user_ids)} 个用户需要签到")

            # 确定是否并发处理
            if concurrent is None:
                concurrent = self.config.get("multi_user.concurrent_processing", False)

            # 执行签到
            if concurrent:
                user_results = self._run_concurrent_checkin(user_ids)
            else:
                user_results = self._run_sequential_checkin(user_ids)

            # 计算总体结果
            duration = time.time() - start_time
            success_users = sum(1 for r in user_results.values() if r.get('success', False))
            failed_users = len(user_ids) - success_users

            result = {
                'success': True,
                'message': '多用户签到任务完成',
                'total_users': len(user_ids),
                'success_users': success_users,
                'failed_users': failed_users,
                'user_results': user_results,
                'duration': duration
            }

            # 记录结果
            self._log_multi_user_result(result)

            return result

        except Exception as e:
            duration = time.time() - start_time
            result = {
                'success': False,
                'message': f'多用户签到任务异常: {e}',
                'total_users': len(user_ids) if user_ids else 0,
                'success_users': 0,
                'failed_users': len(user_ids) if user_ids else 0,
                'user_results': {},
                'duration': duration
            }
            self.logger.error(result['message'])
            return result

        finally:
            self.logger.info("多用户签到任务结束")
            self.logger.info("=" * 50)

    def _run_sequential_checkin(self, user_ids: List[str]) -> Dict:
        """顺序执行用户签到"""
        user_results = {}
        rate_limit_delay = self.config.get("multi_user.rate_limit_delay", 5)

        for i, user_id in enumerate(user_ids):
            self.logger.info(f"正在处理第 {i+1}/{len(user_ids)} 个用户: {user_id}")

            # 执行单用户签到
            result = self.run_user_checkin(user_id)
            user_results[user_id] = result

            # 更新用户状态
            self.config.update_user_status(user_id, result)

            # 速率限制延迟
            if i < len(user_ids) - 1:
                time.sleep(rate_limit_delay)

        return user_results

    def _run_concurrent_checkin(self, user_ids: List[str]) -> Dict:
        """并发执行用户签到"""
        user_results = {}
        max_workers = self.config.get("multi_user.max_concurrent_users", 3)

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交任务
            future_to_user = {
                executor.submit(self.run_user_checkin, user_id): user_id
                for user_id in user_ids
            }

            # 收集结果
            for future in as_completed(future_to_user):
                user_id = future_to_user[future]
                try:
                    result = future.result()
                    user_results[user_id] = result

                    # 更新用户状态
                    self.config.update_user_status(user_id, result)

                except Exception as e:
                    error_result = {
                        'success': False,
                        'message': f'用户签到异常: {e}',
                        'total': 0,
                        'success_count': 0,
                        'failed_count': 0,
                        'already_checked_count': 0,
                        'duration': 0
                    }
                    user_results[user_id] = error_result
                    self.config.update_user_status(user_id, error_result)
                    self.logger.error(f"用户 {user_id} 签到异常: {e}")

        return user_results

    def run_user_checkin(self, user_id: str) -> Dict:
        """
        执行单个用户的签到任务

        Args:
            user_id: 用户ID

        Returns:
            签到结果字典
        """
        start_time = time.time()
        user_data = self.config.get_user(user_id)

        if not user_data:
            return {
                'success': False,
                'message': f'用户 {user_id} 不存在',
                'total': 0,
                'success_count': 0,
                'failed_count': 0,
                'already_checked_count': 0,
                'duration': 0
            }

        username = user_data.get('username', user_id)
        self.logger.info(f"开始为用户 {username}({user_id}) 执行签到")

        try:
            # 获取用户客户端
            client = self.get_user_client(user_id)
            if not client:
                return {
                    'success': False,
                    'message': f'用户 {user_id} 客户端初始化失败',
                    'total': 0,
                    'success_count': 0,
                    'failed_count': 0,
                    'already_checked_count': 0,
                    'duration': time.time() - start_time
                }

            # 获取用户的超话列表
            topics = user_data.get('enabled_topics', [])
            enabled_topics = [t for t in topics if t.get("enabled", True)]

            if not enabled_topics:
                # 尝试自动发现
                if user_data.get('settings', {}).get('auto_discover', True):
                    self.logger.info(f"用户 {user_id} 没有配置超话，尝试自动发现...")
                    discovered_topics = client.get_user_topics()
                    if discovered_topics:
                        # 添加到用户配置
                        for topic in discovered_topics:
                            self.config.add_user_topic(user_id, topic['id'], topic['name'])
                        enabled_topics = discovered_topics

                if not enabled_topics:
                    return {
                        'success': False,
                        'message': f'用户 {user_id} 没有需要签到的超话',
                        'total': 0,
                        'success_count': 0,
                        'failed_count': 0,
                        'already_checked_count': 0,
                        'duration': time.time() - start_time
                    }

            self.logger.info(f"用户 {user_id} 共有 {len(enabled_topics)} 个超话需要签到")

            # 获取用户设置
            settings = user_data.get('settings', {})
            delay = settings.get('delay_between_checkins', 2)

            # 执行批量签到
            batch_result = client.batch_checkin(enabled_topics, delay)

            # 计算耗时
            duration = time.time() - start_time

            # 构造结果
            result = {
                'success': True,
                'message': f'用户 {user_id} 签到任务完成',
                'total': batch_result['total'],
                'success_count': batch_result['success'],
                'failed_count': batch_result['failed'],
                'already_checked_count': batch_result['already_checked'],
                'duration': duration,
                'details': batch_result['details']
            }

            self.logger.info(f"用户 {username}({user_id}) 签到完成: 成功{result['success_count']}, 失败{result['failed_count']}, 已签到{result['already_checked_count']}")

            return result

        except Exception as e:
            duration = time.time() - start_time
            result = {
                'success': False,
                'message': f'用户 {user_id} 签到异常: {e}',
                'total': 0,
                'success_count': 0,
                'failed_count': 0,
                'already_checked_count': 0,
                'duration': duration
            }
            self.logger.error(result['message'])
            return result

    def _log_multi_user_result(self, result: Dict) -> None:
        """记录多用户签到结果"""
        total_users = result['total_users']
        success_users = result['success_users']
        failed_users = result['failed_users']
        duration = result['duration']

        self.logger.info(f"多用户签到结果统计:")
        self.logger.info(f"  总用户数: {total_users}")
        self.logger.info(f"  成功用户: {success_users}")
        self.logger.info(f"  失败用户: {failed_users}")
        self.logger.info(f"  总耗时: {format_duration(duration)}")

        # 记录每个用户的详细结果
        for user_id, user_result in result.get('user_results', {}).items():
            user_data = self.config.get_user(user_id)
            username = user_data.get('username', user_id) if user_data else user_id

            if user_result.get('success', False):
                self.logger.info(f"  ✅ {username}({user_id}): 成功{user_result.get('success_count', 0)}, "
                               f"失败{user_result.get('failed_count', 0)}, "
                               f"已签到{user_result.get('already_checked_count', 0)}")
            else:
                self.logger.warning(f"  ❌ {username}({user_id}): {user_result.get('message', '未知错误')}")

    def discover_user_topics(self, user_id: str) -> List[Dict]:
        """
        发现指定用户的超话

        Args:
            user_id: 用户ID

        Returns:
            发现的超话列表
        """
        try:
            client = self.get_user_client(user_id)
            if not client:
                self.logger.error(f"用户 {user_id} 客户端初始化失败")
                return []

            self.logger.info(f"开始发现用户 {user_id} 的超话...")
            topics = client.get_user_topics()

            if topics:
                self.logger.info(f"为用户 {user_id} 发现 {len(topics)} 个超话")

                # 自动添加到用户配置中
                user_data = self.config.get_user(user_id)
                if user_data and user_data.get('settings', {}).get('auto_discover', True):
                    for topic in topics:
                        self.config.add_user_topic(user_id, topic['id'], topic['name'])
                    self.logger.info(f"已自动添加发现的超话到用户 {user_id} 的配置中")
            else:
                self.logger.warning(f"用户 {user_id} 未发现任何超话，请检查登录状态")

            return topics

        except Exception as e:
            self.logger.error(f"发现用户 {user_id} 超话异常: {e}")
            return []
