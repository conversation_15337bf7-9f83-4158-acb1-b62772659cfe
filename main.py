"""
微博超话自动签到程序
主程序入口 - 支持多用户
"""
import sys
import argparse
import json
from typing import Dict, List, Optional
from config import Config
from checkin_manager import CheckinManager
from scheduler import TaskScheduler
from utils import mask_sensitive_info


class WeiboCheckinApp:
    """微博签到应用主类 - 支持多用户"""

    def __init__(self):
        self.config = Config()
        self.checkin_manager = CheckinManager(self.config)
        self.scheduler = TaskScheduler(self.config)
        self.logger = self.checkin_manager.logger
        self.current_user_id = None  # 当前操作的用户ID
    

    
    def run_checkin(self) -> None:
        """执行签到"""
        print("🚀 开始执行签到任务...")
        result = self.checkin_manager.run_checkin()
        
        if result['success']:
            print(f"✅ 签到任务完成!")
            print(f"📊 统计结果:")
            print(f"   总计: {result['total']} 个超话")
            print(f"   成功: {result['success_count']} 个")
            print(f"   失败: {result['failed_count']} 个")
            print(f"   已签到: {result['already_checked_count']} 个")
            print(f"   耗时: {result['duration']:.1f} 秒")
        else:
            print(f"❌ 签到任务失败: {result['message']}")
    

    
    def start_scheduler(self) -> None:
        """启动定时任务"""
        self.scheduler.start_scheduler()
        status = self.scheduler.get_status()
        
        if status['running']:
            print("✅ 定时任务已启动")
            print(f"📅 计划执行时间: 每日 {status['scheduled_time']}")
            if status['next_run_time']:
                print(f"⏰ 下次执行时间: {status['next_run_time']}")
        else:
            print("❌ 定时任务启动失败")
    
    def stop_scheduler(self) -> None:
        """停止定时任务"""
        self.scheduler.stop_scheduler()
        print("⏹️ 定时任务已停止")
    
    def show_status(self) -> None:
        """显示状态信息"""
        print("📊 程序状态:")

        # 多用户模式状态
        print(f"👥 多用户模式: ✅ 已启用")
        users = self.config.list_users()
        enabled_users = [u for u in users if u['enabled']]
        print(f"👤 用户总数: {len(users)} 个 (启用: {len(enabled_users)} 个)")

        if self.current_user_id:
            user_data = self.config.get_user(self.current_user_id)
            if user_data:
                print(f"🎯 当前用户: {user_data.get('username', self.current_user_id)}")

        # 超话信息
        users = self.config.list_users()
        total_topics = 0
        for user in users:
            user_data = self.config.get_user(user['user_id'])
            if user_data:
                topics = user_data.get('enabled_topics', [])
                total_topics += len(topics)
        print(f"📋 总配置超话: {total_topics} 个")

        # 调度器状态
        scheduler_status = self.scheduler.get_status()
        if scheduler_status['enabled']:
            status_icon = "✅" if scheduler_status['running'] else "⏸️"
            print(f"⏰ 定时任务: {status_icon} 已启用 (每日 {scheduler_status['scheduled_time']})")
            if scheduler_status['next_run_time']:
                print(f"   下次执行: {scheduler_status['next_run_time']}")
        else:
            print(f"⏰ 定时任务: ❌ 未启用")

    # ========== 多用户管理方法 ==========

    def add_user(self, user_id: str, username: str, cookies_str: str) -> None:
        """添加用户"""
        try:
            # 解析cookies
            if cookies_str.startswith('{'):
                cookies = json.loads(cookies_str)
            else:
                cookies = {}
                for item in cookies_str.split(';'):
                    if '=' in item:
                        key, value = item.strip().split('=', 1)
                        cookies[key] = value

            success = self.config.add_user(user_id, username, cookies)
            if success:
                print(f"✅ 已添加用户: {username} (ID: {user_id})")
            else:
                print(f"❌ 添加用户失败")

        except json.JSONDecodeError:
            print("❌ Cookies格式错误，请提供有效的JSON格式或cookie字符串")
        except Exception as e:
            print(f"❌ 添加用户失败: {e}")

    def remove_user(self, user_id: str) -> None:
        """移除用户"""
        user_data = self.config.get_user(user_id)
        if not user_data:
            print(f"❌ 用户 {user_id} 不存在")
            return

        username = user_data.get('username', user_id)
        success = self.config.remove_user(user_id)
        if success:
            print(f"✅ 已移除用户: {username} (ID: {user_id})")
            # 如果移除的是当前用户，清除当前用户设置
            if self.current_user_id == user_id:
                self.current_user_id = None
        else:
            print(f"❌ 移除用户失败")

    def list_users(self) -> None:
        """列出所有用户"""
        users = self.config.list_users()

        if users:
            print(f"👥 用户列表 ({len(users)} 个):")
            for i, user in enumerate(users, 1):
                status = "✅" if user['enabled'] else "❌"
                current_mark = "🎯" if user['user_id'] == self.current_user_id else "  "
                print(f"{current_mark} {i}. {status} {user['username']} (ID: {user['user_id']})")
                print(f"     超话: {user['topics_count']} 个, 签到: {user['total_checkins']} 次, "
                      f"成功率: {user['success_rate']:.1f}%")
        else:
            print("👥 暂无用户")

    def switch_user(self, user_id: str) -> None:
        """切换当前用户"""
        if self.config.set_current_user(user_id):
            user_data = self.config.get_user(user_id)
            username = user_data.get('username', user_id)
            self.current_user_id = user_id
            print(f"✅ 已切换到用户: {username} (ID: {user_id})")
        else:
            print(f"❌ 用户 {user_id} 不存在")

    def update_user_cookies(self, user_id: str, cookies_str: str) -> None:
        """更新用户cookies"""
        try:
            if cookies_str.startswith('{'):
                cookies = json.loads(cookies_str)
            else:
                cookies = {}
                for item in cookies_str.split(';'):
                    if '=' in item:
                        key, value = item.strip().split('=', 1)
                        cookies[key] = value

            success = self.config.update_user_cookies(user_id, cookies)
            if success:
                user_data = self.config.get_user(user_id)
                username = user_data.get('username', user_id) if user_data else user_id
                print(f"✅ 已更新用户 {username} 的cookies")
            else:
                print(f"❌ 更新用户cookies失败")

        except json.JSONDecodeError:
            print("❌ Cookies格式错误，请提供有效的JSON格式或cookie字符串")
        except Exception as e:
            print(f"❌ 更新用户cookies失败: {e}")

    def run_multi_user_checkin(self, user_ids: List[str] = None, concurrent: bool = False) -> None:
        """执行多用户签到"""
        print("🚀 开始执行多用户签到任务...")
        result = self.checkin_manager.run_multi_user_checkin(user_ids, concurrent)

        if result['success']:
            print(f"✅ 多用户签到任务完成!")
            print(f"📊 统计结果:")
            print(f"   总用户数: {result['total_users']} 个")
            print(f"   成功用户: {result['success_users']} 个")
            print(f"   失败用户: {result['failed_users']} 个")
            print(f"   总耗时: {result['duration']:.1f} 秒")

            # 显示每个用户的详细结果
            print(f"📋 详细结果:")
            for user_id, user_result in result.get('user_results', {}).items():
                user_data = self.config.get_user(user_id)
                username = user_data.get('username', user_id) if user_data else user_id

                if user_result.get('success', False):
                    print(f"   ✅ {username}: 成功{user_result.get('success_count', 0)}, "
                          f"失败{user_result.get('failed_count', 0)}, "
                          f"已签到{user_result.get('already_checked_count', 0)}")
                else:
                    print(f"   ❌ {username}: {user_result.get('message', '未知错误')}")
        else:
            print(f"❌ 多用户签到任务失败: {result['message']}")

    def discover_user_topics(self, user_id: str) -> None:
        """发现用户超话"""
        user_data = self.config.get_user(user_id)
        if not user_data:
            print(f"❌ 用户 {user_id} 不存在")
            return

        username = user_data.get('username', user_id)
        print(f"🔍 正在发现用户 {username} 的超话...")
        topics = self.checkin_manager.discover_user_topics(user_id)

        if topics:
            print(f"✅ 为用户 {username} 发现 {len(topics)} 个超话:")
            for i, topic in enumerate(topics, 1):
                print(f"   {i}. {topic['name']} (ID: {topic['id']})")
        else:
            print(f"❌ 用户 {username} 未发现任何超话，请检查登录状态")

    def enable_user(self, user_id: str) -> None:
        """启用用户"""
        success = self.config.enable_user(user_id)
        if success:
            user_data = self.config.get_user(user_id)
            username = user_data.get('username', user_id) if user_data else user_id
            print(f"✅ 已启用用户: {username}")
        else:
            print(f"❌ 启用用户失败")

    def disable_user(self, user_id: str) -> None:
        """禁用用户"""
        success = self.config.disable_user(user_id)
        if success:
            user_data = self.config.get_user(user_id)
            username = user_data.get('username', user_id) if user_data else user_id
            print(f"✅ 已禁用用户: {username}")
        else:
            print(f"❌ 禁用用户失败")
    
    def interactive_mode(self) -> None:
        """交互模式"""
        print("🎯 微博超话自动签到程序 (多用户版)")
        print("=" * 50)

        while True:
            # 显示当前用户信息
            if self.config.is_multi_user_enabled():
                if self.current_user_id:
                    user_data = self.config.get_user(self.current_user_id)
                    username = user_data.get('username', self.current_user_id) if user_data else self.current_user_id
                    print(f"\n🎯 当前用户: {username} (ID: {self.current_user_id})")
                else:
                    print(f"\n👤 当前用户: 未选择")

            print("\n📋 可用操作:")

            # 多用户管理操作
            print("=== 用户管理 ===")
            print("1. 添加用户")
            print("2. 移除用户")
            print("3. 列出所有用户")
            print("4. 切换当前用户")
            print("5. 更新用户cookies")
            print("6. 启用/禁用用户")
            print("=== 签到操作 ===")
            print("7. 执行多用户签到")
            print("8. 执行当前用户签到")
            print("9. 发现当前用户超话")
            print("10. 查看当前用户超话")
            print("11. 为当前用户添加超话")
            print("12. 为当前用户移除超话")
            print("=== 系统操作 ===")
            print("13. 启动定时任务")
            print("14. 停止定时任务")
            print("15. 查看状态")
            print("0. 退出程序")
            
            try:
                choice = input("\n请选择操作 (0-15): ").strip()

                if choice == '0':
                    print("👋 再见!")
                    break

                # 多用户模式操作
                if choice == '1':
                    user_id = input("请输入用户ID: ").strip()
                    username = input("请输入用户名: ").strip()
                    cookies_str = input("请输入cookies (JSON格式或cookie字符串): ").strip()
                    self.add_user(user_id, username, cookies_str)
                elif choice == '2':
                    user_id = input("请输入要移除的用户ID: ").strip()
                    self.remove_user(user_id)
                elif choice == '3':
                    self.list_users()
                elif choice == '4':
                    user_id = input("请输入要切换到的用户ID: ").strip()
                    self.switch_user(user_id)
                elif choice == '5':
                    user_id = input("请输入用户ID: ").strip()
                    cookies_str = input("请输入新的cookies (JSON格式或cookie字符串): ").strip()
                    self.update_user_cookies(user_id, cookies_str)
                elif choice == '6':
                    user_id = input("请输入用户ID: ").strip()
                    action = input("请选择操作 (enable/disable): ").strip().lower()
                    if action == 'enable':
                        self.enable_user(user_id)
                    elif action == 'disable':
                        self.disable_user(user_id)
                    else:
                        print("❌ 无效操作，请输入 enable 或 disable")
                elif choice == '7':
                    concurrent_input = input("是否并发处理? (y/n, 默认n): ").strip().lower()
                    concurrent = concurrent_input in ['y', 'yes', '是']
                    self.run_multi_user_checkin(concurrent=concurrent)
                elif choice == '8':
                    if self.current_user_id:
                        self.config.set_current_user(self.current_user_id)
                        self.run_checkin()
                    else:
                        print("❌ 请先选择当前用户")
                elif choice == '9':
                    if self.current_user_id:
                        self.discover_user_topics(self.current_user_id)
                    else:
                        print("❌ 请先选择当前用户")
                elif choice == '10':
                    if self.current_user_id:
                        self.config.set_current_user(self.current_user_id)
                        user_data = self.config.get_user(self.current_user_id)
                        if user_data:
                            topics = user_data.get('enabled_topics', [])
                            if topics:
                                print(f"📋 当前用户的超话 ({len(topics)} 个):")
                                for i, topic in enumerate(topics, 1):
                                    status = "✅" if topic.get('enabled', True) else "❌"
                                    print(f"   {i}. {status} {topic['name']} (ID: {topic['id']})")
                            else:
                                print("📋 当前用户暂无配置的超话")
                        else:
                            print("❌ 获取用户信息失败")
                    else:
                        print("❌ 请先选择当前用户")
                elif choice == '11':
                    if self.current_user_id:
                        topic_id = input("请输入超话ID: ").strip()
                        topic_name = input("请输入超话名称: ").strip()
                        success = self.config.add_user_topic(self.current_user_id, topic_id, topic_name)
                        if success:
                            print(f"✅ 已为当前用户添加超话: {topic_name}")
                        else:
                            print("❌ 添加超话失败")
                    else:
                        print("❌ 请先选择当前用户")
                elif choice == '12':
                    if self.current_user_id:
                        topic_id = input("请输入要移除的超话ID: ").strip()
                        success = self.config.remove_user_topic(self.current_user_id, topic_id)
                        if success:
                            print(f"✅ 已为当前用户移除超话: {topic_id}")
                        else:
                            print("❌ 移除超话失败")
                    else:
                        print("❌ 请先选择当前用户")
                elif choice == '13':
                    self.start_scheduler()
                elif choice == '14':
                    self.stop_scheduler()
                elif choice == '15':
                    self.show_status()
                else:
                    print("❌ 无效选择，请重新输入")


                    
            except KeyboardInterrupt:
                print("\n👋 程序被中断，再见!")
                break
            except Exception as e:
                print(f"❌ 操作异常: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='微博超话自动签到程序 (多用户版)')



    # 多用户操作
    parser.add_argument('--add-user', nargs=3, metavar=('USER_ID', 'USERNAME', 'COOKIES'), help='添加用户')
    parser.add_argument('--remove-user', metavar='USER_ID', help='移除用户')
    parser.add_argument('--list-users', action='store_true', help='列出所有用户')
    parser.add_argument('--switch-user', metavar='USER_ID', help='切换当前用户')
    parser.add_argument('--update-user-cookies', nargs=2, metavar=('USER_ID', 'COOKIES'), help='更新用户cookies')
    parser.add_argument('--enable-user', metavar='USER_ID', help='启用用户')
    parser.add_argument('--disable-user', metavar='USER_ID', help='禁用用户')
    parser.add_argument('--multi-checkin', action='store_true', help='执行多用户签到')
    parser.add_argument('--concurrent', action='store_true', help='并发处理多用户签到')
    parser.add_argument('--user-checkin', metavar='USER_ID', help='执行指定用户签到')
    parser.add_argument('--discover-user', metavar='USER_ID', help='发现指定用户的超话')

    # 系统操作
    parser.add_argument('--start-scheduler', action='store_true', help='启动定时任务')
    parser.add_argument('--stop-scheduler', action='store_true', help='停止定时任务')
    parser.add_argument('--status', action='store_true', help='显示状态')
    parser.add_argument('--interactive', action='store_true', help='交互模式')
    
    args = parser.parse_args()
    app = WeiboCheckinApp()
    
    try:
        # 多用户操作
        if args.add_user:
            app.add_user(args.add_user[0], args.add_user[1], args.add_user[2])
        elif args.remove_user:
            app.remove_user(args.remove_user)
        elif args.list_users:
            app.list_users()
        elif args.switch_user:
            app.switch_user(args.switch_user)
        elif args.update_user_cookies:
            app.update_user_cookies(args.update_user_cookies[0], args.update_user_cookies[1])
        elif args.enable_user:
            app.enable_user(args.enable_user)
        elif args.disable_user:
            app.disable_user(args.disable_user)
        elif args.multi_checkin:
            app.run_multi_user_checkin(concurrent=args.concurrent)
        elif args.user_checkin:
            app.config.set_current_user(args.user_checkin)
            app.run_checkin()
        elif args.discover_user:
            app.discover_user_topics(args.discover_user)



        # 系统操作
        elif args.start_scheduler:
            app.start_scheduler()
            # 保持程序运行
            try:
                while app.scheduler.is_running():
                    import time
                    time.sleep(60)
            except KeyboardInterrupt:
                app.stop_scheduler()
        elif args.stop_scheduler:
            app.stop_scheduler()
        elif args.status:
            app.show_status()
        elif args.interactive:
            app.interactive_mode()
        else:
            # 默认进入交互模式
            app.interactive_mode()
            
    except KeyboardInterrupt:
        print("\n👋 程序被中断，再见!")
    except Exception as e:
        print(f"❌ 程序异常: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
