('D:\\ai_test\\weibo\\dist\\微博超话自动签到.exe',
 True,
 False,
 False,
 'D:\\ai_test\\weibo\\Lib\\site-packages\\PyInstaller\\bootloader\\images\\icon-console.ico',
 None,
 False,
 False,
 b'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\n<assembly xmlns='
 b'"urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">\n  <trustInfo x'
 b'mlns="urn:schemas-microsoft-com:asm.v3">\n    <security>\n      <requested'
 b'Privileges>\n        <requestedExecutionLevel level="asInvoker" uiAccess='
 b'"false"/>\n      </requestedPrivileges>\n    </security>\n  </trustInfo>\n  '
 b'<compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1">\n    <'
 b'application>\n      <supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f'
 b'0}"/>\n      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/>\n '
 b'     <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/>\n      <s'
 b'upportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/>\n      <supporte'
 b'dOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/>\n    </application>\n  <'
 b'/compatibility>\n  <application xmlns="urn:schemas-microsoft-com:asm.v3">'
 b'\n    <windowsSettings>\n      <longPathAware xmlns="http://schemas.micros'
 b'oft.com/SMI/2016/WindowsSettings">true</longPathAware>\n    </windowsSett'
 b'ings>\n  </application>\n  <dependency>\n    <dependentAssembly>\n      <ass'
 b'emblyIdentity type="win32" name="Microsoft.Windows.Common-Controls" version='
 b'"6.0.0.0" processorArchitecture="*" publicKeyToken="6595b64144ccf1df" langua'
 b'ge="*"/>\n    </dependentAssembly>\n  </dependency>\n</assembly>',
 True,
 False,
 None,
 None,
 None,
 'D:\\ai_test\\weibo\\build\\微博超话自动签到\\微博超话自动签到.pkg',
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz', 'D:\\ai_test\\weibo\\build\\微博超话自动签到\\PYZ-00.pyz', 'PYZ'),
  ('struct',
   'D:\\ai_test\\weibo\\build\\微博超话自动签到\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'D:\\ai_test\\weibo\\build\\微博超话自动签到\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'D:\\ai_test\\weibo\\build\\微博超话自动签到\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'D:\\ai_test\\weibo\\build\\微博超话自动签到\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'D:\\ai_test\\weibo\\build\\微博超话自动签到\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_cryptography_openssl',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('main', 'D:\\ai_test\\weibo\\main.py', 'PYSOURCE'),
  ('selenium\\webdriver\\common\\windows\\selenium-manager.exe',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\selenium\\webdriver\\common\\windows\\selenium-manager.exe',
   'BINARY'),
  ('python311.dll', 'C:\\Software\\Python\\python311.dll', 'BINARY'),
  ('select.pyd', 'C:\\Software\\Python\\DLLs\\select.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Software\\Python\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd', 'C:\\Software\\Python\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'C:\\Software\\Python\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'C:\\Software\\Python\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Software\\Python\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_decimal.pyd', 'C:\\Software\\Python\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_socket.pyd', 'C:\\Software\\Python\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'C:\\Software\\Python\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'C:\\Software\\Python\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'C:\\Software\\Python\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_queue.pyd', 'C:\\Software\\Python\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_cffi_backend.cp311-win_amd64.pyd',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\_cffi_backend.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Software\\Python\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd', 'C:\\Software\\Python\\DLLs\\_asyncio.pyd', 'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp311-win_amd64.pyd',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp311-win_amd64.pyd',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\charset_normalizer\\md.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\etree.cp311-win_amd64.pyd',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\etree.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\_elementpath.cp311-win_amd64.pyd',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\_elementpath.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\sax.cp311-win_amd64.pyd',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\sax.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\objectify.cp311-win_amd64.pyd',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\objectify.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\html\\diff.cp311-win_amd64.pyd',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\html\\diff.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\builder.cp311-win_amd64.pyd',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\builder.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll', 'C:\\Software\\Python\\VCRUNTIME140.dll', 'BINARY'),
  ('libcrypto-1_1.dll',
   'C:\\Software\\Python\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('libssl-1_1.dll', 'C:\\Software\\Python\\DLLs\\libssl-1_1.dll', 'BINARY'),
  ('libffi-8.dll', 'C:\\Software\\Python\\DLLs\\libffi-8.dll', 'BINARY'),
  ('python3.dll', 'C:\\Software\\Python\\python3.dll', 'BINARY'),
  ('config_template.json', 'D:\\ai_test\\weibo\\config_template.json', 'DATA'),
  ('cryptography-45.0.4.dist-info\\INSTALLER',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography-45.0.4.dist-info\\INSTALLER',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\REQUESTED',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography-45.0.4.dist-info\\REQUESTED',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\licenses\\LICENSE.BSD',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography-45.0.4.dist-info\\licenses\\LICENSE.BSD',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\WHEEL',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography-45.0.4.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\licenses\\LICENSE',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography-45.0.4.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\RECORD',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography-45.0.4.dist-info\\RECORD',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\METADATA',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography-45.0.4.dist-info\\METADATA',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\licenses\\LICENSE.APACHE',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography-45.0.4.dist-info\\licenses\\LICENSE.APACHE',
   'DATA'),
  ('selenium\\webdriver\\common\\devtools\\v137\\py.typed',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\selenium\\webdriver\\common\\devtools\\v137\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\common\\devtools\\v135\\py.typed',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\selenium\\webdriver\\common\\devtools\\v135\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\common\\devtools\\v136\\py.typed',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\selenium\\webdriver\\common\\devtools\\v136\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\common\\macos\\selenium-manager',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\selenium\\webdriver\\common\\macos\\selenium-manager',
   'DATA'),
  ('selenium\\webdriver\\remote\\getAttribute.js',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\selenium\\webdriver\\remote\\getAttribute.js',
   'DATA'),
  ('selenium\\py.typed',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\selenium\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\firefox\\webdriver_prefs.json',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\selenium\\webdriver\\firefox\\webdriver_prefs.json',
   'DATA'),
  ('selenium\\webdriver\\common\\mutation-listener.js',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\selenium\\webdriver\\common\\mutation-listener.js',
   'DATA'),
  ('selenium\\webdriver\\remote\\findElements.js',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\selenium\\webdriver\\remote\\findElements.js',
   'DATA'),
  ('selenium\\webdriver\\common\\linux\\selenium-manager',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\selenium\\webdriver\\common\\linux\\selenium-manager',
   'DATA'),
  ('selenium\\webdriver\\remote\\isDisplayed.js',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\selenium\\webdriver\\remote\\isDisplayed.js',
   'DATA'),
  ('certifi\\py.typed',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('certifi\\cacert.pem',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'DATA'),
  ('fake_useragent-2.2.0.dist-info\\licenses\\LICENSE',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\fake_useragent-2.2.0.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('fake_useragent-2.2.0.dist-info\\licenses\\AUTHORS',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\fake_useragent-2.2.0.dist-info\\licenses\\AUTHORS',
   'DATA'),
  ('fake_useragent-2.2.0.dist-info\\RECORD',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\fake_useragent-2.2.0.dist-info\\RECORD',
   'DATA'),
  ('fake_useragent-2.2.0.dist-info\\INSTALLER',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\fake_useragent-2.2.0.dist-info\\INSTALLER',
   'DATA'),
  ('fake_useragent-2.2.0.dist-info\\WHEEL',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\fake_useragent-2.2.0.dist-info\\WHEEL',
   'DATA'),
  ('fake_useragent-2.2.0.dist-info\\top_level.txt',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\fake_useragent-2.2.0.dist-info\\top_level.txt',
   'DATA'),
  ('fake_useragent-2.2.0.dist-info\\REQUESTED',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\fake_useragent-2.2.0.dist-info\\REQUESTED',
   'DATA'),
  ('fake_useragent-2.2.0.dist-info\\METADATA',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\fake_useragent-2.2.0.dist-info\\METADATA',
   'DATA'),
  ('base_library.zip',
   'D:\\ai_test\\weibo\\build\\微博超话自动签到\\base_library.zip',
   'DATA')],
 [],
 False,
 False,
 1749560887,
 [('run.exe',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\PyInstaller\\bootloader\\Windows-64bit-intel\\run.exe',
   'EXECUTABLE')],
 'C:\\Software\\Python\\python311.dll')
