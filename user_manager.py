"""
用户管理器模块
负责多用户账户的管理
"""
import json
import os
import time
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
from crypto_utils import create_crypto_utils, is_encryption_available


class UserManager:
    """用户管理器类"""
    
    def __init__(self, users_file: str = "users.json", enable_encryption: bool = True):
        """
        初始化用户管理器
        
        Args:
            users_file: 用户数据文件路径
            enable_encryption: 是否启用加密存储
        """
        self.users_file = users_file
        self.enable_encryption = enable_encryption and is_encryption_available()
        self.crypto_utils = create_crypto_utils() if self.enable_encryption else None
        self.users_data = self._load_users()
    
    def _load_users(self) -> Dict:
        """加载用户数据"""
        if os.path.exists(self.users_file):
            try:
                with open(self.users_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                # 如果启用了加密，解密用户数据
                if self.enable_encryption and data.get('encrypted', False):
                    users = {}
                    for user_id, encrypted_data in data.get('users', {}).items():
                        try:
                            users[user_id] = self.crypto_utils.decrypt_data(encrypted_data)
                        except Exception as e:
                            print(f"警告: 解密用户 {user_id} 数据失败: {e}")
                            continue
                    return {'users': users, 'encrypted': True}
                else:
                    return data
                    
            except (json.JSONDecodeError, FileNotFoundError):
                pass
        
        # 返回默认结构
        return {
            'users': {},
            'encrypted': self.enable_encryption,
            'created_at': datetime.now(timezone.utc).isoformat(),
            'last_updated': datetime.now(timezone.utc).isoformat()
        }
    
    def _save_users(self) -> None:
        """保存用户数据"""
        self.users_data['last_updated'] = datetime.now(timezone.utc).isoformat()
        
        # 确保目录存在
        users_dir = os.path.dirname(self.users_file)
        if users_dir:
            os.makedirs(users_dir, exist_ok=True)
        
        # 准备保存的数据
        save_data = {
            'encrypted': self.enable_encryption,
            'created_at': self.users_data.get('created_at'),
            'last_updated': self.users_data['last_updated'],
            'users': {}
        }
        
        # 如果启用加密，加密用户数据
        if self.enable_encryption:
            for user_id, user_data in self.users_data['users'].items():
                try:
                    save_data['users'][user_id] = self.crypto_utils.encrypt_data(user_data)
                except Exception as e:
                    print(f"警告: 加密用户 {user_id} 数据失败: {e}")
                    continue
        else:
            save_data['users'] = self.users_data['users']
        
        with open(self.users_file, 'w', encoding='utf-8') as f:
            json.dump(save_data, f, ensure_ascii=False, indent=2)
    
    def add_user(self, user_id: str, username: str, cookies: Dict, 
                 enabled_topics: List[Dict] = None, settings: Dict = None) -> bool:
        """
        添加用户
        
        Args:
            user_id: 用户ID（唯一标识）
            username: 用户名
            cookies: 用户cookies
            enabled_topics: 启用的超话列表
            settings: 用户设置
            
        Returns:
            True如果添加成功，否则False
        """
        try:
            if enabled_topics is None:
                enabled_topics = []
            if settings is None:
                settings = {}
            
            user_data = {
                'user_id': user_id,
                'username': username,
                'cookies': cookies,
                'enabled_topics': enabled_topics,
                'settings': {
                    'auto_discover': settings.get('auto_discover', True),
                    'max_topics': settings.get('max_topics', 50),
                    'retry_times': settings.get('retry_times', 3),
                    'delay_between_checkins': settings.get('delay_between_checkins', 2),
                    'enabled': settings.get('enabled', True),
                    **settings
                },
                'status': {
                    'last_checkin_time': None,
                    'last_checkin_result': None,
                    'total_checkins': 0,
                    'success_count': 0,
                    'failed_count': 0,
                    'created_at': datetime.now(timezone.utc).isoformat(),
                    'updated_at': datetime.now(timezone.utc).isoformat()
                }
            }
            
            self.users_data['users'][user_id] = user_data
            self._save_users()
            return True
            
        except Exception as e:
            print(f"添加用户失败: {e}")
            return False
    
    def remove_user(self, user_id: str) -> bool:
        """
        移除用户
        
        Args:
            user_id: 用户ID
            
        Returns:
            True如果移除成功，否则False
        """
        try:
            if user_id in self.users_data['users']:
                del self.users_data['users'][user_id]
                self._save_users()
                return True
            return False
            
        except Exception as e:
            print(f"移除用户失败: {e}")
            return False
    
    def get_user(self, user_id: str) -> Optional[Dict]:
        """
        获取用户信息
        
        Args:
            user_id: 用户ID
            
        Returns:
            用户信息字典，如果不存在返回None
        """
        return self.users_data['users'].get(user_id)
    
    def get_all_users(self) -> Dict[str, Dict]:
        """
        获取所有用户信息
        
        Returns:
            所有用户信息字典
        """
        return self.users_data['users'].copy()
    
    def get_enabled_users(self) -> Dict[str, Dict]:
        """
        获取所有启用的用户
        
        Returns:
            启用的用户信息字典
        """
        enabled_users = {}
        for user_id, user_data in self.users_data['users'].items():
            if user_data.get('settings', {}).get('enabled', True):
                enabled_users[user_id] = user_data
        return enabled_users
    
    def update_user_cookies(self, user_id: str, cookies: Dict) -> bool:
        """
        更新用户cookies
        
        Args:
            user_id: 用户ID
            cookies: 新的cookies
            
        Returns:
            True如果更新成功，否则False
        """
        try:
            if user_id in self.users_data['users']:
                self.users_data['users'][user_id]['cookies'] = cookies
                self.users_data['users'][user_id]['status']['updated_at'] = datetime.now(timezone.utc).isoformat()
                self._save_users()
                return True
            return False
            
        except Exception as e:
            print(f"更新用户cookies失败: {e}")
            return False

    def update_user_topics(self, user_id: str, enabled_topics: List[Dict]) -> bool:
        """
        更新用户超话列表

        Args:
            user_id: 用户ID
            enabled_topics: 启用的超话列表

        Returns:
            True如果更新成功，否则False
        """
        try:
            if user_id in self.users_data['users']:
                self.users_data['users'][user_id]['enabled_topics'] = enabled_topics
                self.users_data['users'][user_id]['status']['updated_at'] = datetime.now(timezone.utc).isoformat()
                self._save_users()
                return True
            return False

        except Exception as e:
            print(f"更新用户超话列表失败: {e}")
            return False

    def add_user_topic(self, user_id: str, topic_id: str, topic_name: str) -> bool:
        """
        为用户添加超话

        Args:
            user_id: 用户ID
            topic_id: 超话ID
            topic_name: 超话名称

        Returns:
            True如果添加成功，否则False
        """
        try:
            if user_id in self.users_data['users']:
                topics = self.users_data['users'][user_id]['enabled_topics']
                topic_info = {"id": topic_id, "name": topic_name, "enabled": True}

                # 检查是否已存在
                for i, topic in enumerate(topics):
                    if topic.get("id") == topic_id:
                        topics[i] = topic_info
                        break
                else:
                    topics.append(topic_info)

                self.users_data['users'][user_id]['status']['updated_at'] = datetime.now(timezone.utc).isoformat()
                self._save_users()
                return True
            return False

        except Exception as e:
            print(f"添加用户超话失败: {e}")
            return False

    def remove_user_topic(self, user_id: str, topic_id: str) -> bool:
        """
        移除用户超话

        Args:
            user_id: 用户ID
            topic_id: 超话ID

        Returns:
            True如果移除成功，否则False
        """
        try:
            if user_id in self.users_data['users']:
                topics = self.users_data['users'][user_id]['enabled_topics']
                topics = [t for t in topics if t.get("id") != topic_id]
                self.users_data['users'][user_id]['enabled_topics'] = topics
                self.users_data['users'][user_id]['status']['updated_at'] = datetime.now(timezone.utc).isoformat()
                self._save_users()
                return True
            return False

        except Exception as e:
            print(f"移除用户超话失败: {e}")
            return False

    def update_user_status(self, user_id: str, checkin_result: Dict) -> bool:
        """
        更新用户签到状态

        Args:
            user_id: 用户ID
            checkin_result: 签到结果

        Returns:
            True如果更新成功，否则False
        """
        try:
            if user_id in self.users_data['users']:
                status = self.users_data['users'][user_id]['status']
                status['last_checkin_time'] = datetime.now(timezone.utc).isoformat()
                status['last_checkin_result'] = checkin_result
                status['total_checkins'] += 1

                if checkin_result.get('success', False):
                    status['success_count'] += 1
                else:
                    status['failed_count'] += 1

                status['updated_at'] = datetime.now(timezone.utc).isoformat()
                self._save_users()
                return True
            return False

        except Exception as e:
            print(f"更新用户状态失败: {e}")
            return False

    def enable_user(self, user_id: str) -> bool:
        """
        启用用户

        Args:
            user_id: 用户ID

        Returns:
            True如果操作成功，否则False
        """
        try:
            if user_id in self.users_data['users']:
                self.users_data['users'][user_id]['settings']['enabled'] = True
                self.users_data['users'][user_id]['status']['updated_at'] = datetime.now(timezone.utc).isoformat()
                self._save_users()
                return True
            return False

        except Exception as e:
            print(f"启用用户失败: {e}")
            return False

    def disable_user(self, user_id: str) -> bool:
        """
        禁用用户

        Args:
            user_id: 用户ID

        Returns:
            True如果操作成功，否则False
        """
        try:
            if user_id in self.users_data['users']:
                self.users_data['users'][user_id]['settings']['enabled'] = False
                self.users_data['users'][user_id]['status']['updated_at'] = datetime.now(timezone.utc).isoformat()
                self._save_users()
                return True
            return False

        except Exception as e:
            print(f"禁用用户失败: {e}")
            return False

    def get_user_statistics(self, user_id: str) -> Optional[Dict]:
        """
        获取用户统计信息

        Args:
            user_id: 用户ID

        Returns:
            用户统计信息，如果用户不存在返回None
        """
        user_data = self.get_user(user_id)
        if not user_data:
            return None

        status = user_data.get('status', {})
        return {
            'user_id': user_id,
            'username': user_data.get('username', ''),
            'enabled': user_data.get('settings', {}).get('enabled', True),
            'topics_count': len(user_data.get('enabled_topics', [])),
            'total_checkins': status.get('total_checkins', 0),
            'success_count': status.get('success_count', 0),
            'failed_count': status.get('failed_count', 0),
            'success_rate': (status.get('success_count', 0) / max(status.get('total_checkins', 1), 1)) * 100,
            'last_checkin_time': status.get('last_checkin_time'),
            'created_at': status.get('created_at'),
            'updated_at': status.get('updated_at')
        }

    def list_users(self) -> List[Dict]:
        """
        列出所有用户的基本信息

        Returns:
            用户信息列表
        """
        users_list = []
        for user_id in self.users_data['users']:
            stats = self.get_user_statistics(user_id)
            if stats:
                users_list.append(stats)
        return users_list
