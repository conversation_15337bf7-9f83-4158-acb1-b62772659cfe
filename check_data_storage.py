#!/usr/bin/env python3
"""
检查数据存储情况的脚本
"""

import json
import os
from config import Config
from user_manager import UserManager
from crypto_utils import CryptoUtils

def check_data_storage():
    """检查数据存储情况"""
    print("🔍 检查数据存储情况")
    print("=" * 60)
    
    # 1. 检查 users.json 文件
    users_file = "users.json"
    if os.path.exists(users_file):
        print(f"📁 {users_file} 文件存在")
        
        with open(users_file, 'r', encoding='utf-8') as f:
            raw_data = json.load(f)
        
        print(f"🔐 加密状态: {'是' if raw_data.get('encrypted', False) else '否'}")
        print(f"📅 创建时间: {raw_data.get('created_at', '未知')}")
        print(f"📅 更新时间: {raw_data.get('last_updated', '未知')}")
        print(f"👥 用户数量: {len(raw_data.get('users', {}))}")
        
        # 显示原始数据结构
        print(f"\n📋 原始文件内容:")
        for user_id, user_data in raw_data.get('users', {}).items():
            print(f"   用户ID: {user_id}")
            if isinstance(user_data, str):
                print(f"   数据: [加密字符串，长度: {len(user_data)} 字符]")
            else:
                print(f"   数据: [明文数据]")
    else:
        print(f"❌ {users_file} 文件不存在")
    
    print("\n" + "-" * 60)
    
    # 2. 通过程序接口查看解密后的数据
    try:
        config = Config()
        users = config.list_users()
        
        print(f"🔓 通过程序接口解密后的数据:")
        print(f"👥 用户总数: {len(users)}")
        
        for user in users:
            user_id = user['user_id']
            username = user['username']
            print(f"\n👤 用户: {username} (ID: {user_id})")
            
            # 获取详细数据
            user_data = config.get_user(user_id)
            if user_data:
                topics = user_data.get('enabled_topics', [])
                print(f"📋 超话数量: {len(topics)}")
                
                if topics:
                    print(f"📋 超话列表:")
                    for i, topic in enumerate(topics, 1):
                        print(f"   {i}. {topic.get('name', '未知')} (ID: {topic.get('id', '未知')})")
                else:
                    print(f"📋 超话列表: 暂无")
                
                # 检查cookies
                cookies = user_data.get('cookies', {})
                if cookies:
                    print(f"🍪 Cookies: 已设置 ({len(cookies)} 个字段)")
                else:
                    print(f"🍪 Cookies: 未设置")
            else:
                print(f"❌ 无法获取用户详细数据")
    
    except Exception as e:
        print(f"❌ 通过程序接口读取数据失败: {e}")
    
    print("\n" + "-" * 60)
    
    # 3. 检查配置文件
    config_file = "config.json"
    if os.path.exists(config_file):
        print(f"📁 {config_file} 文件存在")
        
        with open(config_file, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        multi_user_enabled = config_data.get('multi_user', {}).get('enabled', False)
        print(f"👥 多用户模式: {'启用' if multi_user_enabled else '禁用'}")
        
        # 检查是否有传统的单用户超话配置
        checkin_topics = config_data.get('checkin', {}).get('enabled_topics', [])
        print(f"📋 传统配置中的超话: {len(checkin_topics)} 个")
        
        if checkin_topics:
            print(f"📋 传统超话列表:")
            for i, topic in enumerate(checkin_topics, 1):
                print(f"   {i}. {topic.get('name', '未知')} (ID: {topic.get('id', '未知')})")
    else:
        print(f"❌ {config_file} 文件不存在")
    
    print("\n" + "=" * 60)
    print("📝 总结:")
    print("1. 超话数据存储在 users.json 文件中")
    print("2. 数据使用 AES-256 加密算法加密存储")
    print("3. 每个用户的超话列表在其用户数据的 'enabled_topics' 字段中")
    print("4. 只能通过程序的配置系统来查看和修改超话数据")
    print("5. 直接查看 users.json 文件只能看到加密后的字符串")

if __name__ == "__main__":
    check_data_storage()
