#!/usr/bin/env python3
"""
创建发布包脚本
生成完整的用户分发包
"""

import os
import sys
import shutil
import zipfile
import time
from datetime import datetime
from pathlib import Path

# 版本信息
VERSION = "2.0.0"
APP_NAME = "微博超话自动签到"
RELEASE_NAME = f"{APP_NAME}_v{VERSION}"

def create_release_package():
    """创建发布包"""
    print(f"📦 创建 {APP_NAME} v{VERSION} 发布包")
    print("=" * 60)
    
    # 检查dist目录
    if not os.path.exists('dist'):
        print("❌ dist目录不存在，请先运行打包脚本")
        return False
    
    exe_file = os.path.join('dist', f'{APP_NAME}.exe')
    if not os.path.exists(exe_file):
        print("❌ 可执行文件不存在，请先运行打包脚本")
        return False
    
    # 创建发布目录
    release_dir = f"release/{RELEASE_NAME}"
    if os.path.exists(release_dir):
        shutil.rmtree(release_dir)
    os.makedirs(release_dir, exist_ok=True)
    
    print(f"📁 创建发布目录: {release_dir}")
    
    # 复制主要文件
    files_to_copy = [
        ('dist/微博超话自动签到.exe', '微博超话自动签到.exe'),
        ('dist/config_template.json', 'config_template.json'),
        ('README.md', 'README.md'),
        ('MULTI_USER_GUIDE.md', '多用户使用指南.md'),
        ('PACKAGING_GUIDE.md', '打包指南.md'),
    ]
    
    for src, dst in files_to_copy:
        if os.path.exists(src):
            dst_path = os.path.join(release_dir, dst)
            shutil.copy2(src, dst_path)
            print(f"✅ 已复制: {dst}")
        else:
            print(f"⚠️  文件不存在: {src}")
    
    # 创建目录结构
    dirs_to_create = ['logs', 'docs']
    for dir_name in dirs_to_create:
        dir_path = os.path.join(release_dir, dir_name)
        os.makedirs(dir_path, exist_ok=True)
        print(f"📁 已创建目录: {dir_name}")
    
    # 复制文档到docs目录
    doc_files = [
        ('TECHNICAL_DOCUMENTATION.md', 'docs/技术文档.md'),
        ('BUG_FIX_SUMMARY.md', 'docs/问题修复记录.md'),
        ('LEGACY_CODE_CLEANUP_SUMMARY.md', 'docs/代码清理记录.md'),
    ]
    
    for src, dst in doc_files:
        if os.path.exists(src):
            dst_path = os.path.join(release_dir, dst)
            shutil.copy2(src, dst_path)
            print(f"📋 已复制文档: {dst}")
    
    # 创建快速启动脚本
    create_launcher_scripts(release_dir)
    
    # 创建详细的使用说明
    create_detailed_readme(release_dir)
    
    # 创建版本信息文件
    create_version_info(release_dir)
    
    # 创建ZIP压缩包
    create_zip_package(release_dir)
    
    print(f"\n🎉 发布包创建完成!")
    print(f"📂 发布目录: {os.path.abspath(release_dir)}")
    print(f"📦 压缩包: {os.path.abspath(f'release/{RELEASE_NAME}.zip')}")
    
    return True

def create_launcher_scripts(release_dir):
    """创建启动脚本"""
    print("📝 创建启动脚本...")
    
    # Windows批处理文件
    bat_content = f"""@echo off
chcp 65001 >nul
title {APP_NAME} v{VERSION}

echo.
echo ========================================
echo 🎯 {APP_NAME} v{VERSION}
echo ========================================
echo.

:: 检查是否首次运行
if not exist config.json (
    echo 📝 首次运行，正在初始化...
    echo.
)

:: 显示菜单
:menu
echo 请选择操作:
echo.
echo 1. 查看程序状态
echo 2. 添加用户账户
echo 3. 列出所有用户
echo 4. 执行多用户签到
echo 5. 启动交互模式
echo 6. 查看帮助信息
echo 0. 退出程序
echo.

set /p choice="请输入选择 (0-6): "

if "%choice%"=="1" goto status
if "%choice%"=="2" goto add_user
if "%choice%"=="3" goto list_users
if "%choice%"=="4" goto multi_checkin
if "%choice%"=="5" goto interactive
if "%choice%"=="6" goto help
if "%choice%"=="0" goto exit
goto menu

:status
echo.
echo 📊 查看程序状态...
微博超话自动签到.exe --status
echo.
pause
goto menu

:add_user
echo.
echo 👤 添加用户账户...
echo 请准备好用户ID、用户名和cookies信息
echo.
set /p user_id="请输入用户ID: "
set /p username="请输入用户名: "
set /p cookies="请输入cookies: "
微博超话自动签到.exe --add-user "%user_id%" "%username%" "%cookies%"
echo.
pause
goto menu

:list_users
echo.
echo 👥 用户列表...
微博超话自动签到.exe --list-users
echo.
pause
goto menu

:multi_checkin
echo.
echo 🚀 执行多用户签到...
微博超话自动签到.exe --multi-checkin
echo.
pause
goto menu

:interactive
echo.
echo 🎮 启动交互模式...
微博超话自动签到.exe --interactive
goto menu

:help
echo.
echo 📖 帮助信息...
微博超话自动签到.exe --help
echo.
pause
goto menu

:exit
echo.
echo 👋 再见!
exit
"""
    
    bat_file = os.path.join(release_dir, "启动程序.bat")
    with open(bat_file, 'w', encoding='utf-8') as f:
        f.write(bat_content)
    print("✅ 已创建: 启动程序.bat")
    
    # 快速签到脚本
    quick_checkin_content = f"""@echo off
chcp 65001 >nul
title {APP_NAME} - 快速签到

echo 🚀 执行多用户签到...
微博超话自动签到.exe --multi-checkin

echo.
echo 签到完成! 按任意键退出...
pause >nul
"""
    
    quick_file = os.path.join(release_dir, "快速签到.bat")
    with open(quick_file, 'w', encoding='utf-8') as f:
        f.write(quick_checkin_content)
    print("✅ 已创建: 快速签到.bat")

def create_detailed_readme(release_dir):
    """创建详细的使用说明"""
    print("📝 创建详细使用说明...")
    
    readme_content = f"""# {APP_NAME} v{VERSION} 使用说明

## 🎯 快速开始

### 方法一：使用启动脚本（推荐新手）
1. 双击 `启动程序.bat` 
2. 按照菜单提示操作
3. 首次使用请先添加用户账户

### 方法二：直接运行程序
1. 双击 `{APP_NAME}.exe`
2. 使用命令行参数或交互模式

### 方法三：快速签到
1. 双击 `快速签到.bat`
2. 自动执行所有用户的签到任务

## 📋 主要功能

### 1. 用户管理
- ✅ 添加/删除用户账户
- ✅ 启用/禁用用户
- ✅ 更新用户cookies
- ✅ 查看用户列表和统计

### 2. 超话管理
- ✅ 自动发现用户关注的超话
- ✅ 手动添加/删除超话
- ✅ 查看超话列表

### 3. 签到功能
- ✅ 多用户批量签到
- ✅ 单用户签到
- ✅ 并发处理（可选）
- ✅ 定时任务

### 4. 系统功能
- ✅ 详细日志记录
- ✅ 配置管理
- ✅ 状态监控

## 🔧 常用命令

### 用户管理
```bash
# 添加用户
{APP_NAME}.exe --add-user USER_ID USERNAME COOKIES

# 列出用户
{APP_NAME}.exe --list-users

# 启用/禁用用户
{APP_NAME}.exe --enable-user USER_ID
{APP_NAME}.exe --disable-user USER_ID
```

### 签到操作
```bash
# 多用户签到
{APP_NAME}.exe --multi-checkin

# 单用户签到
{APP_NAME}.exe --user-checkin USER_ID

# 发现超话
{APP_NAME}.exe --discover-user USER_ID
```

### 系统操作
```bash
# 查看状态
{APP_NAME}.exe --status

# 交互模式
{APP_NAME}.exe --interactive

# 定时任务
{APP_NAME}.exe --start-scheduler
```

## 📁 文件说明

- `{APP_NAME}.exe` - 主程序
- `启动程序.bat` - 图形化启动脚本
- `快速签到.bat` - 一键签到脚本
- `config.json` - 主配置文件（自动生成）
- `users.json` - 用户数据文件（自动生成）
- `config_template.json` - 配置模板
- `logs/` - 日志文件目录
- `docs/` - 详细文档目录

## ⚠️ 注意事项

### 1. 首次使用
- 程序会自动创建配置文件
- 请先添加至少一个用户账户
- 建议先测试单用户签到

### 2. Cookies获取
- 登录微博网页版
- 按F12打开开发者工具
- 在Network标签页找到请求
- 复制Cookie字段的值

### 3. 安全建议
- 定期备份用户数据文件
- 保护好cookies信息
- 注意程序更新

### 4. 故障排除
- 查看logs目录下的日志文件
- 检查网络连接
- 确认cookies是否过期

## 📞 技术支持

如遇问题请：
1. 查看日志文件了解详细错误信息
2. 参考docs目录下的技术文档
3. 检查配置文件是否正确

## 📝 版本信息

- 版本: {VERSION}
- 构建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 支持系统: Windows 7/10/11
- Python版本: 3.8+

## 🎉 更新日志

### v{VERSION}
- ✅ 统一多用户架构
- ✅ 移除传统单用户模式
- ✅ 优化API解析逻辑
- ✅ 修复已知问题
- ✅ 改进用户体验

---

感谢使用 {APP_NAME}！
"""
    
    readme_file = os.path.join(release_dir, "使用说明.txt")
    with open(readme_file, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    print("✅ 已创建: 使用说明.txt")

def create_version_info(release_dir):
    """创建版本信息文件"""
    print("📝 创建版本信息...")
    
    version_info = f"""# {APP_NAME} 版本信息

版本号: {VERSION}
构建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
程序类型: 可执行文件 (.exe)
支持系统: Windows 7/10/11 (64位)
文件大小: {get_file_size('dist/微博超话自动签到.exe')}

## 技术信息
- Python版本: 3.11+
- 打包工具: PyInstaller 6.14.1
- 架构: 统一多用户架构
- 加密支持: ✅ 支持
- 并发处理: ✅ 支持

## 功能特性
- ✅ 多用户管理
- ✅ 批量签到
- ✅ 自动发现超话
- ✅ 定时任务
- ✅ 详细日志
- ✅ 配置管理

## 依赖库
- requests >= 2.28.0
- beautifulsoup4 >= 4.11.0
- schedule >= 1.2.0
- selenium >= 4.8.0
- lxml >= 4.9.0
- fake-useragent >= 1.4.0
- cryptography >= 3.4.8

构建环境: Windows 10
"""
    
    version_file = os.path.join(release_dir, "版本信息.txt")
    with open(version_file, 'w', encoding='utf-8') as f:
        f.write(version_info)
    print("✅ 已创建: 版本信息.txt")

def get_file_size(file_path):
    """获取文件大小"""
    try:
        size_bytes = os.path.getsize(file_path)
        size_mb = size_bytes / (1024 * 1024)
        return f"{size_mb:.1f} MB"
    except:
        return "未知"

def create_zip_package(release_dir):
    """创建ZIP压缩包"""
    print("📦 创建ZIP压缩包...")
    
    zip_path = f"release/{RELEASE_NAME}.zip"
    
    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(release_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arc_path = os.path.relpath(file_path, release_dir)
                zipf.write(file_path, arc_path)
                print(f"   📄 {arc_path}")
    
    zip_size = get_file_size(zip_path)
    print(f"✅ 压缩包创建完成: {zip_path} ({zip_size})")

def main():
    """主函数"""
    if not create_release_package():
        sys.exit(1)
    
    print("\n" + "=" * 60)
    print("🎉 发布包创建完成!")
    print(f"📂 发布目录: release/{RELEASE_NAME}/")
    print(f"📦 压缩包: release/{RELEASE_NAME}.zip")
    print("=" * 60)
    
    # 询问是否打开发布目录
    try:
        response = input("是否打开发布目录? (y/n): ").strip().lower()
        if response in ['y', 'yes', '是']:
            release_path = os.path.abspath(f"release/{RELEASE_NAME}")
            if sys.platform == 'win32':
                os.startfile(release_path)
            elif sys.platform == 'darwin':
                subprocess.run(['open', release_path])
            else:
                subprocess.run(['xdg-open', release_path])
    except KeyboardInterrupt:
        print("\n👋 再见!")

if __name__ == "__main__":
    main()
