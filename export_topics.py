#!/usr/bin/env python3
"""
导出用户超话数据到明文文件
"""

import json
import os
from datetime import datetime
from config import Config

def export_topics_to_file():
    """导出超话数据到明文文件"""
    try:
        # 初始化配置
        config = Config()
        
        # 获取所有用户
        users = config.list_users()
        
        if not users:
            print("❌ 没有找到任何用户")
            return
        
        # 准备导出数据
        export_data = {
            "export_time": datetime.now().isoformat(),
            "total_users": len(users),
            "users": {}
        }
        
        print(f"📤 开始导出 {len(users)} 个用户的超话数据...")
        
        for user in users:
            user_id = user['user_id']
            username = user['username']
            
            print(f"📋 处理用户: {username} (ID: {user_id})")
            
            # 获取用户详细信息
            user_data = config.get_user(user_id)
            if user_data:
                topics = user_data.get('enabled_topics', [])
                settings = user_data.get('settings', {})
                status = user_data.get('status', {})
                
                # 构建导出数据（不包含敏感的cookies）
                export_user_data = {
                    "user_id": user_id,
                    "username": username,
                    "enabled": user['enabled'],
                    "topics_count": len(topics),
                    "total_checkins": user['total_checkins'],
                    "success_rate": user['success_rate'],
                    "enabled_topics": topics,
                    "settings": settings,
                    "status": {
                        "last_checkin_time": status.get('last_checkin_time'),
                        "last_checkin_result": status.get('last_checkin_result'),
                        "total_checkins": status.get('total_checkins', 0),
                        "success_count": status.get('success_count', 0),
                        "failed_count": status.get('failed_count', 0),
                        "created_at": status.get('created_at'),
                        "updated_at": status.get('updated_at')
                    }
                }
                
                export_data["users"][user_id] = export_user_data
                print(f"   ✅ 导出 {len(topics)} 个超话")
            else:
                print(f"   ❌ 无法获取用户详细数据")
        
        # 保存到文件
        output_file = "topics_export.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n✅ 导出完成！")
        print(f"📁 文件保存为: {output_file}")
        print(f"📊 导出统计:")
        print(f"   用户数量: {export_data['total_users']}")
        
        total_topics = sum(len(user_data.get('enabled_topics', [])) 
                          for user_data in export_data['users'].values())
        print(f"   超话总数: {total_topics}")
        
        # 显示详细信息
        print(f"\n📋 详细信息:")
        for user_id, user_data in export_data['users'].items():
            print(f"   👤 {user_data['username']} (ID: {user_id})")
            print(f"      超话数量: {user_data['topics_count']}")
            print(f"      签到次数: {user_data['total_checkins']}")
            print(f"      成功率: {user_data['success_rate']:.1f}%")
            
            if user_data['enabled_topics']:
                print(f"      超话列表:")
                for i, topic in enumerate(user_data['enabled_topics'], 1):
                    status = "✅" if topic.get('enabled', True) else "❌"
                    print(f"        {i}. {status} {topic.get('name', '未知')}")
                    print(f"           ID: {topic.get('id', '未知')}")
        
        return output_file
    
    except Exception as e:
        print(f"❌ 导出失败: {e}")
        return None

def create_simple_topics_list():
    """创建简单的超话列表文件"""
    try:
        config = Config()
        users = config.list_users()
        
        output_file = "topics_list.txt"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("微博超话自动签到程序 - 超话列表\n")
            f.write("=" * 50 + "\n")
            f.write(f"导出时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            for user in users:
                user_id = user['user_id']
                username = user['username']
                
                f.write(f"👤 用户: {username} (ID: {user_id})\n")
                f.write(f"状态: {'✅ 启用' if user['enabled'] else '❌ 禁用'}\n")
                f.write(f"签到统计: {user['total_checkins']} 次, 成功率 {user['success_rate']:.1f}%\n")
                
                user_data = config.get_user(user_id)
                if user_data:
                    topics = user_data.get('enabled_topics', [])
                    f.write(f"超话数量: {len(topics)} 个\n\n")
                    
                    if topics:
                        f.write("超话列表:\n")
                        for i, topic in enumerate(topics, 1):
                            status = "✅" if topic.get('enabled', True) else "❌"
                            f.write(f"  {i:2d}. {status} {topic.get('name', '未知')}\n")
                            f.write(f"      ID: {topic.get('id', '未知')}\n")
                    else:
                        f.write("超话列表: 暂无\n")
                else:
                    f.write("超话数量: 无法获取\n")
                
                f.write("\n" + "-" * 50 + "\n\n")
        
        print(f"✅ 简单列表已保存为: {output_file}")
        return output_file
    
    except Exception as e:
        print(f"❌ 创建简单列表失败: {e}")
        return None

if __name__ == "__main__":
    print("🚀 开始导出超话数据...")
    
    # 导出详细的JSON文件
    json_file = export_topics_to_file()
    
    print("\n" + "=" * 60)
    
    # 创建简单的文本列表
    txt_file = create_simple_topics_list()
    
    print(f"\n📁 导出的文件:")
    if json_file:
        print(f"   📄 {json_file} - 详细的JSON格式数据")
    if txt_file:
        print(f"   📄 {txt_file} - 简单的文本列表")
    
    print(f"\n💡 提示:")
    print(f"   - JSON文件包含完整的用户和超话数据")
    print(f"   - 文本文件是易读的超话列表")
    print(f"   - 这些文件不包含敏感的cookies信息")
    print(f"   - 可以安全地分享或备份这些文件")
