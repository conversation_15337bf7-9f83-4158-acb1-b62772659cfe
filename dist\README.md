# 微博超话自动签到程序

一个用Python开发的微博超话自动签到工具，支持批量签到、定时任务、配置管理等功能。

## 功能特性

- ✅ **自动签到**: 支持批量签到用户关注的超话
- ✅ **智能发现**: 自动发现用户关注的超话并添加到签到列表
- ✅ **定时任务**: 支持设置每日定时自动签到
- ✅ **配置管理**: 灵活的配置文件管理，支持超话的添加/移除
- ✅ **日志记录**: 详细的日志记录，方便问题排查
- ✅ **错误重试**: 内置重试机制，提高签到成功率
- ✅ **交互界面**: 友好的命令行交互界面
- ✅ **状态检查**: 实时查看签到状态和程序运行状态

## 安装依赖

```bash
pip install -r requirements.txt
```

## 快速开始

### 1. 交互模式（推荐）

```bash
python main.py --interactive
```

### 2. 命令行模式

#### 设置登录cookies
```bash
python main.py --cookies "your_cookies_here"
```

#### 发现关注的超话
```bash
python main.py --discover
```

#### 执行签到
```bash
python main.py --checkin
```

#### 启动定时任务
```bash
python main.py --start-scheduler
```

## 详细使用说明

### 获取Cookies

1. 打开浏览器，登录微博网页版
2. 按F12打开开发者工具
3. 切换到Network标签页
4. 刷新页面或访问任意微博页面
5. 找到请求头中的Cookie字段，复制完整的cookie值

### 配置文件说明

程序会自动创建 `config.json` 配置文件，主要配置项：

```json
{
  "user": {
    "cookies": {}  // 登录cookies
  },
  "checkin": {
    "enabled_topics": [],  // 启用的超话列表
    "auto_discover": true,  // 是否自动发现超话
    "max_topics": 50,  // 最大超话数量
    "retry_times": 3,  // 重试次数
    "delay_between_checkins": 2  // 签到间隔（秒）
  },
  "schedule": {
    "enabled": false,  // 是否启用定时任务
    "time": "09:00",  // 执行时间
    "timezone": "Asia/Shanghai"  // 时区
  },
  "logging": {
    "level": "INFO",  // 日志级别
    "file": "logs/weibo_checkin.log"  // 日志文件
  }
}
```

### 命令行参数

```bash
python main.py [选项]

选项:
  --cookies COOKIES     设置登录cookies
  --checkin            执行签到
  --discover           发现关注的超话
  --list               列出配置的超话
  --add-topic ID NAME  添加超话
  --remove-topic ID    移除超话
  --start-scheduler    启动定时任务
  --stop-scheduler     停止定时任务
  --status             显示状态
  --interactive        交互模式
```

## 使用示例

### 基本使用流程

1. **设置cookies**
   ```bash
   python main.py --cookies "your_cookies_string"
   ```

2. **发现超话**
   ```bash
   python main.py --discover
   ```

3. **执行签到**
   ```bash
   python main.py --checkin
   ```

4. **设置定时任务**
   ```bash
   python main.py --start-scheduler
   ```

### 手动管理超话

```bash
# 添加超话
python main.py --add-topic "1234567890" "超话名称"

# 移除超话
python main.py --remove-topic "1234567890"

# 查看超话列表
python main.py --list
```

## 注意事项

1. **Cookies安全**: 请妥善保管你的cookies，不要泄露给他人
2. **请求频率**: 程序内置了请求间隔，避免过于频繁的请求
3. **账号安全**: 建议使用小号进行测试，避免主账号被限制
4. **网络环境**: 确保网络连接稳定，避免签到失败
5. **定期更新**: cookies有时效性，需要定期更新

## 故障排除

### 常见问题

1. **Cookies失效**
   - 重新获取并设置cookies
   - 检查是否正确复制了完整的cookie字符串

2. **签到失败**
   - 检查网络连接
   - 确认超话ID是否正确
   - 查看日志文件获取详细错误信息

3. **定时任务不执行**
   - 确认定时任务已启用
   - 检查系统时间是否正确
   - 查看日志文件确认任务状态

### 日志文件

程序运行日志保存在 `logs/weibo_checkin.log`，包含详细的运行信息和错误信息。

## 免责声明

本工具仅供学习和研究使用，使用者需要遵守微博的使用条款和相关法律法规。作者不对使用本工具造成的任何后果承担责任。

## 许可证

MIT License
