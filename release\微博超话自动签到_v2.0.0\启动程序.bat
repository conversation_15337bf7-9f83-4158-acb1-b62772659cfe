@echo off
chcp 65001 >nul
title 微博超话自动签到 v2.0.0

echo.
echo ========================================
echo 🎯 微博超话自动签到 v2.0.0
echo ========================================
echo.

:: 检查是否首次运行
if not exist config.json (
    echo 📝 首次运行，正在初始化...
    echo.
)

:: 显示菜单
:menu
echo 请选择操作:
echo.
echo 1. 查看程序状态
echo 2. 添加用户账户
echo 3. 列出所有用户
echo 4. 执行多用户签到
echo 5. 启动交互模式
echo 6. 查看帮助信息
echo 0. 退出程序
echo.

set /p choice="请输入选择 (0-6): "

if "%choice%"=="1" goto status
if "%choice%"=="2" goto add_user
if "%choice%"=="3" goto list_users
if "%choice%"=="4" goto multi_checkin
if "%choice%"=="5" goto interactive
if "%choice%"=="6" goto help
if "%choice%"=="0" goto exit
goto menu

:status
echo.
echo 📊 查看程序状态...
微博超话自动签到.exe --status
echo.
pause
goto menu

:add_user
echo.
echo 👤 添加用户账户...
echo 请准备好用户ID、用户名和cookies信息
echo.
set /p user_id="请输入用户ID: "
set /p username="请输入用户名: "
set /p cookies="请输入cookies: "
微博超话自动签到.exe --add-user "%user_id%" "%username%" "%cookies%"
echo.
pause
goto menu

:list_users
echo.
echo 👥 用户列表...
微博超话自动签到.exe --list-users
echo.
pause
goto menu

:multi_checkin
echo.
echo 🚀 执行多用户签到...
微博超话自动签到.exe --multi-checkin
echo.
pause
goto menu

:interactive
echo.
echo 🎮 启动交互模式...
微博超话自动签到.exe --interactive
goto menu

:help
echo.
echo 📖 帮助信息...
微博超话自动签到.exe --help
echo.
pause
goto menu

:exit
echo.
echo 👋 再见!
exit
