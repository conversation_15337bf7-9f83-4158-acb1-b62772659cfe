#!/usr/bin/env python3
"""
多用户功能测试脚本
"""
import sys
import json
from config import Config
from user_manager import UserManager
from checkin_manager import CheckinManager

def test_user_manager():
    """测试用户管理器"""
    print("🧪 测试用户管理器...")
    
    # 创建用户管理器
    user_manager = UserManager("test_users.json", enable_encryption=False)
    
    # 测试添加用户
    test_cookies = {
        "SUB": "test_sub_cookie",
        "SUBP": "test_subp_cookie",
        "SSOLoginState": "test_sso_state"
    }
    
    success = user_manager.add_user(
        "test_user1", 
        "测试用户1", 
        test_cookies,
        [{"id": "123456", "name": "测试超话", "enabled": True}]
    )
    
    if success:
        print("✅ 添加用户成功")
    else:
        print("❌ 添加用户失败")
        return False
    
    # 测试获取用户
    user_data = user_manager.get_user("test_user1")
    if user_data:
        print(f"✅ 获取用户成功: {user_data['username']}")
    else:
        print("❌ 获取用户失败")
        return False
    
    # 测试列出用户
    users = user_manager.list_users()
    print(f"✅ 用户列表: {len(users)} 个用户")
    
    # 测试用户统计
    stats = user_manager.get_user_statistics("test_user1")
    if stats:
        print(f"✅ 用户统计: {stats['username']}, 超话数: {stats['topics_count']}")
    
    return True

def test_config():
    """测试配置管理"""
    print("\n🧪 测试配置管理...")
    
    # 创建配置管理器
    config = Config("test_config.json", "test_users.json")
    
    # 测试多用户模式
    if config.is_multi_user_enabled():
        print("✅ 多用户模式已启用")
    else:
        print("❌ 多用户模式未启用")
    
    # 测试获取用户列表
    users = config.list_users()
    print(f"✅ 配置中的用户数: {len(users)}")
    
    return True

def test_crypto():
    """测试加密功能"""
    print("\n🧪 测试加密功能...")
    
    try:
        from crypto_utils import create_crypto_utils, is_encryption_available
        
        if not is_encryption_available():
            print("❌ 加密功能不可用")
            return False
        
        crypto = create_crypto_utils()
        
        # 测试数据加密解密
        test_data = {"test": "数据", "number": 123}
        encrypted = crypto.encrypt_data(test_data)
        decrypted = crypto.decrypt_data(encrypted)
        
        if decrypted == test_data:
            print("✅ 数据加密解密测试通过")
        else:
            print("❌ 数据加密解密测试失败")
            return False
        
        # 测试cookies加密解密
        test_cookies = {"SUB": "test_cookie", "SUBP": "test_cookie2"}
        encrypted_cookies = crypto.encrypt_cookies(test_cookies)
        decrypted_cookies = crypto.decrypt_cookies(encrypted_cookies)
        
        if decrypted_cookies == test_cookies:
            print("✅ Cookies加密解密测试通过")
        else:
            print("❌ Cookies加密解密测试失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 加密功能测试异常: {e}")
        return False

def test_checkin_manager():
    """测试签到管理器"""
    print("\n🧪 测试签到管理器...")
    
    try:
        config = Config("test_config.json", "test_users.json")
        checkin_manager = CheckinManager(config)
        
        print("✅ 签到管理器创建成功")
        
        # 测试多用户模式检测
        if config.is_multi_user_enabled():
            print("✅ 多用户模式检测正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 签到管理器测试异常: {e}")
        return False

def cleanup():
    """清理测试文件"""
    import os
    test_files = ["test_users.json", "test_config.json"]
    for file in test_files:
        if os.path.exists(file):
            os.remove(file)
            print(f"🗑️ 已清理测试文件: {file}")

def main():
    """主测试函数"""
    print("🚀 开始多用户功能测试")
    print("=" * 50)
    
    tests = [
        ("用户管理器", test_user_manager),
        ("配置管理", test_config),
        ("加密功能", test_crypto),
        ("签到管理器", test_checkin_manager),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                print(f"✅ {test_name} 测试通过")
                passed += 1
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！多用户功能正常")
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
    
    # 清理测试文件
    cleanup()
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
