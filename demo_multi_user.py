#!/usr/bin/env python3
"""
多用户功能演示脚本
"""
import json
from main import WeiboCheckinApp

def demo_multi_user():
    """演示多用户功能"""
    print("🎯 微博超话自动签到程序 - 多用户功能演示")
    print("=" * 60)
    
    # 创建应用实例
    app = WeiboCheckinApp()
    
    # 演示用户数据
    demo_users = [
        {
            "user_id": "demo_user1",
            "username": "演示用户1",
            "cookies": {
                "SUB": "demo_sub_cookie_1",
                "SUBP": "demo_subp_cookie_1",
                "SSOLoginState": "demo_sso_state_1"
            }
        },
        {
            "user_id": "demo_user2", 
            "username": "演示用户2",
            "cookies": {
                "SUB": "demo_sub_cookie_2",
                "SUBP": "demo_subp_cookie_2",
                "SSOLoginState": "demo_sso_state_2"
            }
        }
    ]
    
    print("\n📋 步骤1: 添加演示用户")
    print("-" * 30)
    
    for user in demo_users:
        cookies_str = json.dumps(user["cookies"])
        print(f"正在添加用户: {user['username']} (ID: {user['user_id']})")
        app.add_user(user["user_id"], user["username"], cookies_str)
    
    print("\n📋 步骤2: 查看用户列表")
    print("-" * 30)
    app.list_users()
    
    print("\n📋 步骤3: 切换到第一个用户")
    print("-" * 30)
    app.switch_user("demo_user1")
    
    print("\n📋 步骤4: 为当前用户添加演示超话")
    print("-" * 30)
    demo_topics = [
        {"id": "1001234567890", "name": "演示超话1"},
        {"id": "1001234567891", "name": "演示超话2"}
    ]
    
    for topic in demo_topics:
        success = app.config.add_user_topic(
            "demo_user1", 
            topic["id"], 
            topic["name"]
        )
        if success:
            print(f"✅ 已为用户添加超话: {topic['name']}")
    
    print("\n📋 步骤5: 为第二个用户添加不同的超话")
    print("-" * 30)
    demo_topics_2 = [
        {"id": "1001234567892", "name": "演示超话3"},
        {"id": "1001234567893", "name": "演示超话4"}
    ]
    
    for topic in demo_topics_2:
        success = app.config.add_user_topic(
            "demo_user2", 
            topic["id"], 
            topic["name"]
        )
        if success:
            print(f"✅ 已为用户2添加超话: {topic['name']}")
    
    print("\n📋 步骤6: 查看用户统计信息")
    print("-" * 30)
    for user in demo_users:
        stats = app.config.get_user_statistics(user["user_id"])
        if stats:
            print(f"👤 {stats['username']}:")
            print(f"   - 用户ID: {stats['user_id']}")
            print(f"   - 状态: {'✅ 启用' if stats['enabled'] else '❌ 禁用'}")
            print(f"   - 超话数量: {stats['topics_count']}")
            print(f"   - 签到次数: {stats['total_checkins']}")
            print(f"   - 成功率: {stats['success_rate']:.1f}%")
    
    print("\n📋 步骤7: 演示用户管理操作")
    print("-" * 30)
    
    # 禁用一个用户
    print("禁用演示用户2...")
    app.disable_user("demo_user2")
    
    # 再次查看用户列表
    print("\n更新后的用户列表:")
    app.list_users()
    
    print("\n📋 步骤8: 查看程序状态")
    print("-" * 30)
    app.show_status()
    
    print("\n📋 步骤9: 演示多用户配置")
    print("-" * 30)
    
    # 显示多用户配置
    multi_user_config = {
        "enabled": app.config.is_multi_user_enabled(),
        "total_users": len(app.config.get_all_users()),
        "enabled_users": len(app.config.get_enabled_users()),
        "concurrent_processing": app.config.get("multi_user.concurrent_processing", False),
        "max_concurrent_users": app.config.get("multi_user.max_concurrent_users", 3),
        "rate_limit_delay": app.config.get("multi_user.rate_limit_delay", 5)
    }
    
    print("🔧 多用户配置:")
    for key, value in multi_user_config.items():
        print(f"   {key}: {value}")
    
    print("\n📋 步骤10: 清理演示数据")
    print("-" * 30)
    
    # 询问是否清理演示数据
    cleanup = input("是否清理演示数据? (y/n, 默认n): ").strip().lower()
    
    if cleanup in ['y', 'yes', '是']:
        for user in demo_users:
            app.remove_user(user["user_id"])
        print("✅ 演示数据已清理")
    else:
        print("ℹ️ 演示数据已保留，您可以继续使用或手动清理")
    
    print("\n🎉 多用户功能演示完成！")
    print("=" * 60)
    
    print("\n💡 接下来您可以:")
    print("1. 运行 'python main.py --interactive' 进入交互模式")
    print("2. 使用 'python main.py --help' 查看所有命令行选项")
    print("3. 阅读 MULTI_USER_GUIDE.md 了解详细使用说明")
    print("4. 配置真实的用户cookies开始使用")

if __name__ == "__main__":
    demo_multi_user()
