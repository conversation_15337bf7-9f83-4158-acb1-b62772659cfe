"""
演示微博程序中的请求头管理
"""
import requests


def demo_weibo_headers():
    """演示微博程序的请求头管理"""
    
    # 创建Session并设置默认请求头（模拟我们的程序）
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    })
    
    print("🔧 Session默认请求头:")
    for key, value in session.headers.items():
        print(f"   {key}: {value}")
    print()
    
    # 场景1: API请求
    print("📤 场景1: API请求")
    api_headers = {
        'Accept': 'application/json, text/plain, */*',  # 覆盖默认Accept
        'X-Requested-With': 'XMLHttpRequest',           # 新增
        'Referer': 'https://weibo.com/mygroups'         # 新增
    }
    
    print("   临时请求头:")
    for key, value in api_headers.items():
        print(f"     {key}: {value}")
    
    # 合并后的最终请求头
    final_headers = {**session.headers, **api_headers}
    print("   最终发送的请求头:")
    for key, value in final_headers.items():
        print(f"     {key}: {value}")
    print()
    
    # 场景2: 页面访问
    print("📤 场景2: 页面访问")
    page_headers = {
        'Referer': 'https://weibo.com/mygroups'  # 只添加Referer
    }
    
    print("   临时请求头:")
    for key, value in page_headers.items():
        print(f"     {key}: {value}")
    
    final_headers = {**session.headers, **page_headers}
    print("   最终发送的请求头:")
    for key, value in final_headers.items():
        print(f"     {key}: {value}")
    print()
    
    # 场景3: 签到请求
    print("📤 场景3: 签到请求")
    checkin_headers = {
        'Referer': 'https://weibo.com/p/123456/super_index',  # 特定Referer
        'X-Requested-With': 'XMLHttpRequest',                # AJAX标识
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'  # 表单类型
    }
    
    print("   临时请求头:")
    for key, value in checkin_headers.items():
        print(f"     {key}: {value}")
    
    final_headers = {**session.headers, **checkin_headers}
    print("   最终发送的请求头:")
    for key, value in final_headers.items():
        print(f"     {key}: {value}")
    print()
    
    # 场景4: 验证Session默认头未被修改
    print("📤 场景4: 验证Session默认头未被修改")
    print("   Session默认请求头（应该保持不变）:")
    for key, value in session.headers.items():
        print(f"     {key}: {value}")
    print()


def demo_header_priority():
    """演示请求头优先级"""
    print("🎯 请求头优先级演示")
    
    session = requests.Session()
    session.headers['Accept'] = 'text/html'  # Session默认
    session.headers['User-Agent'] = 'SessionAgent/1.0'
    
    # 临时请求头会覆盖Session默认头
    temp_headers = {
        'Accept': 'application/json',  # 覆盖Session的Accept
        'Referer': 'https://example.com'  # 新增
        # User-Agent没有指定，会使用Session的默认值
    }
    
    print("Session默认头:")
    for key, value in session.headers.items():
        print(f"   {key}: {value}")
    
    print("临时请求头:")
    for key, value in temp_headers.items():
        print(f"   {key}: {value}")
    
    # 模拟requests的合并逻辑
    final_headers = {**session.headers, **temp_headers}
    print("最终请求头:")
    for key, value in final_headers.items():
        print(f"   {key}: {value}")
    
    print("📋 注意:")
    print("   - Accept被临时请求头覆盖: text/html → application/json")
    print("   - User-Agent保持Session默认值: SessionAgent/1.0")
    print("   - Referer是新增的临时请求头")
    print()


def main():
    print("🎯 微博程序请求头管理演示")
    print("=" * 60)
    
    demo_weibo_headers()
    demo_header_priority()
    
    print("✅ 演示完成!")
    print("\n💡 总结:")
    print("1. Session.headers设置默认请求头，所有请求都会携带")
    print("2. 单次请求的headers参数可以覆盖或新增请求头")
    print("3. 临时请求头不会影响Session的默认设置")
    print("4. 这种机制让我们可以灵活地为不同类型的请求设置不同的请求头")


if __name__ == "__main__":
    main()
