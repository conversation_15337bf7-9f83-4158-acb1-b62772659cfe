(['D:\\ai_test\\weibo\\main.py'],
 ['D:\\ai_test\\weibo'],
 ['requests', 'beautifulsoup4', 'schedule', 'selenium', 'cryptography'],
 [('D:\\ai_test\\weibo\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('D:\\ai_test\\weibo\\Lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 ['tkinter', 'matplotlib', 'numpy', '__main__'],
 [],
 False,
 {},
 0,
 [],
 [('config_template.json', 'D:\\ai_test\\weibo\\config_template.json', 'DATA')],
 '3.11.3 (tags/v3.11.3:f3909b8, Apr  4 2023, 23:49:59) [MSC v.1934 64 bit '
 '(AMD64)]',
 [('pyi_rth_cryptography_openssl',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('main', 'D:\\ai_test\\weibo\\main.py', 'PYSOURCE')],
 [('subprocess', 'C:\\Software\\Python\\Lib\\subprocess.py', 'PYMODULE'),
  ('selectors', 'C:\\Software\\Python\\Lib\\selectors.py', 'PYMODULE'),
  ('contextlib', 'C:\\Software\\Python\\Lib\\contextlib.py', 'PYMODULE'),
  ('threading', 'C:\\Software\\Python\\Lib\\threading.py', 'PYMODULE'),
  ('_threading_local',
   'C:\\Software\\Python\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('signal', 'C:\\Software\\Python\\Lib\\signal.py', 'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Software\\Python\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Software\\Python\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Software\\Python\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Software\\Python\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Software\\Python\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Software\\Python\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('xmlrpc.client', 'C:\\Software\\Python\\Lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('xmlrpc', 'C:\\Software\\Python\\Lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('gzip', 'C:\\Software\\Python\\Lib\\gzip.py', 'PYMODULE'),
  ('_compression', 'C:\\Software\\Python\\Lib\\_compression.py', 'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Software\\Python\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\Software\\Python\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml', 'C:\\Software\\Python\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Software\\Python\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Software\\Python\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Software\\Python\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib', 'C:\\Software\\Python\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('fnmatch', 'C:\\Software\\Python\\Lib\\fnmatch.py', 'PYMODULE'),
  ('getpass', 'C:\\Software\\Python\\Lib\\getpass.py', 'PYMODULE'),
  ('nturl2path', 'C:\\Software\\Python\\Lib\\nturl2path.py', 'PYMODULE'),
  ('ftplib', 'C:\\Software\\Python\\Lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'C:\\Software\\Python\\Lib\\netrc.py', 'PYMODULE'),
  ('shlex', 'C:\\Software\\Python\\Lib\\shlex.py', 'PYMODULE'),
  ('mimetypes', 'C:\\Software\\Python\\Lib\\mimetypes.py', 'PYMODULE'),
  ('getopt', 'C:\\Software\\Python\\Lib\\getopt.py', 'PYMODULE'),
  ('gettext', 'C:\\Software\\Python\\Lib\\gettext.py', 'PYMODULE'),
  ('copy', 'C:\\Software\\Python\\Lib\\copy.py', 'PYMODULE'),
  ('email.utils', 'C:\\Software\\Python\\Lib\\email\\utils.py', 'PYMODULE'),
  ('email.charset', 'C:\\Software\\Python\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.encoders',
   'C:\\Software\\Python\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('quopri', 'C:\\Software\\Python\\Lib\\quopri.py', 'PYMODULE'),
  ('email.errors', 'C:\\Software\\Python\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.quoprimime',
   'C:\\Software\\Python\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Software\\Python\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Software\\Python\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('calendar', 'C:\\Software\\Python\\Lib\\calendar.py', 'PYMODULE'),
  ('random', 'C:\\Software\\Python\\Lib\\random.py', 'PYMODULE'),
  ('statistics', 'C:\\Software\\Python\\Lib\\statistics.py', 'PYMODULE'),
  ('fractions', 'C:\\Software\\Python\\Lib\\fractions.py', 'PYMODULE'),
  ('numbers', 'C:\\Software\\Python\\Lib\\numbers.py', 'PYMODULE'),
  ('http.cookiejar',
   'C:\\Software\\Python\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http', 'C:\\Software\\Python\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('ssl', 'C:\\Software\\Python\\Lib\\ssl.py', 'PYMODULE'),
  ('urllib.response',
   'C:\\Software\\Python\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error', 'C:\\Software\\Python\\Lib\\urllib\\error.py', 'PYMODULE'),
  ('string', 'C:\\Software\\Python\\Lib\\string.py', 'PYMODULE'),
  ('hashlib', 'C:\\Software\\Python\\Lib\\hashlib.py', 'PYMODULE'),
  ('email', 'C:\\Software\\Python\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email.parser', 'C:\\Software\\Python\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email._policybase',
   'C:\\Software\\Python\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Software\\Python\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.message', 'C:\\Software\\Python\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.policy', 'C:\\Software\\Python\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.contentmanager',
   'C:\\Software\\Python\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Software\\Python\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Software\\Python\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Software\\Python\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Software\\Python\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Software\\Python\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email.header', 'C:\\Software\\Python\\Lib\\email\\header.py', 'PYMODULE'),
  ('bisect', 'C:\\Software\\Python\\Lib\\bisect.py', 'PYMODULE'),
  ('xml.sax', 'C:\\Software\\Python\\Lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Software\\Python\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Software\\Python\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Software\\Python\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('urllib.parse', 'C:\\Software\\Python\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('http.client', 'C:\\Software\\Python\\Lib\\http\\client.py', 'PYMODULE'),
  ('decimal', 'C:\\Software\\Python\\Lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'C:\\Software\\Python\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('contextvars', 'C:\\Software\\Python\\Lib\\contextvars.py', 'PYMODULE'),
  ('datetime', 'C:\\Software\\Python\\Lib\\datetime.py', 'PYMODULE'),
  ('_strptime', 'C:\\Software\\Python\\Lib\\_strptime.py', 'PYMODULE'),
  ('base64', 'C:\\Software\\Python\\Lib\\base64.py', 'PYMODULE'),
  ('hmac', 'C:\\Software\\Python\\Lib\\hmac.py', 'PYMODULE'),
  ('struct', 'C:\\Software\\Python\\Lib\\struct.py', 'PYMODULE'),
  ('socket', 'C:\\Software\\Python\\Lib\\socket.py', 'PYMODULE'),
  ('tempfile', 'C:\\Software\\Python\\Lib\\tempfile.py', 'PYMODULE'),
  ('shutil', 'C:\\Software\\Python\\Lib\\shutil.py', 'PYMODULE'),
  ('zipfile', 'C:\\Software\\Python\\Lib\\zipfile.py', 'PYMODULE'),
  ('py_compile', 'C:\\Software\\Python\\Lib\\py_compile.py', 'PYMODULE'),
  ('importlib.machinery',
   'C:\\Software\\Python\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Software\\Python\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Software\\Python\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Software\\Python\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Software\\Python\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.abc', 'C:\\Software\\Python\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Software\\Python\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Software\\Python\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'C:\\Software\\Python\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Software\\Python\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Software\\Python\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Software\\Python\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Software\\Python\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Software\\Python\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Software\\Python\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Software\\Python\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Software\\Python\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Software\\Python\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('textwrap', 'C:\\Software\\Python\\Lib\\textwrap.py', 'PYMODULE'),
  ('csv', 'C:\\Software\\Python\\Lib\\csv.py', 'PYMODULE'),
  ('importlib.readers',
   'C:\\Software\\Python\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Software\\Python\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Software\\Python\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('tokenize', 'C:\\Software\\Python\\Lib\\tokenize.py', 'PYMODULE'),
  ('token', 'C:\\Software\\Python\\Lib\\token.py', 'PYMODULE'),
  ('pathlib', 'C:\\Software\\Python\\Lib\\pathlib.py', 'PYMODULE'),
  ('importlib.util',
   'C:\\Software\\Python\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('tarfile', 'C:\\Software\\Python\\Lib\\tarfile.py', 'PYMODULE'),
  ('lzma', 'C:\\Software\\Python\\Lib\\lzma.py', 'PYMODULE'),
  ('bz2', 'C:\\Software\\Python\\Lib\\bz2.py', 'PYMODULE'),
  ('logging', 'C:\\Software\\Python\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('pickle', 'C:\\Software\\Python\\Lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'C:\\Software\\Python\\Lib\\pprint.py', 'PYMODULE'),
  ('dataclasses', 'C:\\Software\\Python\\Lib\\dataclasses.py', 'PYMODULE'),
  ('inspect', 'C:\\Software\\Python\\Lib\\inspect.py', 'PYMODULE'),
  ('dis', 'C:\\Software\\Python\\Lib\\dis.py', 'PYMODULE'),
  ('opcode', 'C:\\Software\\Python\\Lib\\opcode.py', 'PYMODULE'),
  ('ast', 'C:\\Software\\Python\\Lib\\ast.py', 'PYMODULE'),
  ('_compat_pickle',
   'C:\\Software\\Python\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Software\\Python\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Software\\Python\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Software\\Python\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Software\\Python\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Software\\Python\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Software\\Python\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Software\\Python\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('ctypes', 'C:\\Software\\Python\\Lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._endian',
   'C:\\Software\\Python\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Software\\Python\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Software\\Python\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Software\\Python\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('queue', 'C:\\Software\\Python\\Lib\\queue.py', 'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Software\\Python\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Software\\Python\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Software\\Python\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Software\\Python\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets', 'C:\\Software\\Python\\Lib\\secrets.py', 'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Software\\Python\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Software\\Python\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('runpy', 'C:\\Software\\Python\\Lib\\runpy.py', 'PYMODULE'),
  ('pkgutil', 'C:\\Software\\Python\\Lib\\pkgutil.py', 'PYMODULE'),
  ('zipimport', 'C:\\Software\\Python\\Lib\\zipimport.py', 'PYMODULE'),
  ('multiprocessing',
   'C:\\Software\\Python\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('cryptography',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers.algorithms',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.padding',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('ipaddress', 'C:\\Software\\Python\\Lib\\ipaddress.py', 'PYMODULE'),
  ('cryptography.x509.extensions',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.verification',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\x509\\verification.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.utils',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('__future__', 'C:\\Software\\Python\\Lib\\__future__.py', 'PYMODULE'),
  ('selenium',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\selenium\\__init__.py',
   'PYMODULE'),
  ('schedule',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\schedule\\__init__.py',
   'PYMODULE'),
  ('requests',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.status_codes',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.compat',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('http.cookies', 'C:\\Software\\Python\\Lib\\http\\cookies.py', 'PYMODULE'),
  ('requests.models',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('idna',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.package_data',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.intranges',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.core',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.uts46data',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('idna.idnadata',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('requests.hooks',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.cookies',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.auth',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('urllib3.util',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('typing_extensions',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Software\\Python\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio', 'C:\\Software\\Python\\Lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Software\\Python\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log', 'C:\\Software\\Python\\Lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Software\\Python\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Software\\Python\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Software\\Python\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Software\\Python\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Software\\Python\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads',
   'C:\\Software\\Python\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'C:\\Software\\Python\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'C:\\Software\\Python\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Software\\Python\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Software\\Python\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Software\\Python\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Software\\Python\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Software\\Python\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Software\\Python\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Software\\Python\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Software\\Python\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Software\\Python\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Software\\Python\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Software\\Python\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Software\\Python\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.tasks', 'C:\\Software\\Python\\Lib\\asyncio\\tasks.py', 'PYMODULE'),
  ('asyncio.locks', 'C:\\Software\\Python\\Lib\\asyncio\\locks.py', 'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Software\\Python\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'C:\\Software\\Python\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Software\\Python\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Software\\Python\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Software\\Python\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Software\\Python\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Software\\Python\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Software\\Python\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Software\\Python\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Software\\Python\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Software\\Python\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.response',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.connection',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3._version',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.http2',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.fields',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('requests.api',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.sessions',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.adapters',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('socks', 'D:\\ai_test\\weibo\\Lib\\site-packages\\socks.py', 'PYMODULE'),
  ('urllib3.poolmanager',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('requests.__version__',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests.utils',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('requests.certs',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('certifi',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('requests.packages',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('requests.exceptions',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('urllib3',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('_py_abc', 'C:\\Software\\Python\\Lib\\_py_abc.py', 'PYMODULE'),
  ('tracemalloc', 'C:\\Software\\Python\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('stringprep', 'C:\\Software\\Python\\Lib\\stringprep.py', 'PYMODULE'),
  ('utils', 'D:\\ai_test\\weibo\\utils.py', 'PYMODULE'),
  ('bs4',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\bs4\\__init__.py',
   'PYMODULE'),
  ('bs4._warnings',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\bs4\\_warnings.py',
   'PYMODULE'),
  ('bs4.exceptions',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\bs4\\exceptions.py',
   'PYMODULE'),
  ('bs4._typing',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\bs4\\_typing.py',
   'PYMODULE'),
  ('bs4.filter',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\bs4\\filter.py',
   'PYMODULE'),
  ('bs4.formatter',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\bs4\\formatter.py',
   'PYMODULE'),
  ('bs4.element',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\bs4\\element.py',
   'PYMODULE'),
  ('bs4._deprecation',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\bs4\\_deprecation.py',
   'PYMODULE'),
  ('bs4.css',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\bs4\\css.py',
   'PYMODULE'),
  ('soupsieve',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\soupsieve\\__init__.py',
   'PYMODULE'),
  ('soupsieve.css_parser',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\soupsieve\\css_parser.py',
   'PYMODULE'),
  ('soupsieve.css_match',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\soupsieve\\css_match.py',
   'PYMODULE'),
  ('soupsieve.css_types',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\soupsieve\\css_types.py',
   'PYMODULE'),
  ('soupsieve.pretty',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\soupsieve\\pretty.py',
   'PYMODULE'),
  ('soupsieve.util',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\soupsieve\\util.py',
   'PYMODULE'),
  ('soupsieve.__meta__',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\soupsieve\\__meta__.py',
   'PYMODULE'),
  ('bs4.dammit',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\bs4\\dammit.py',
   'PYMODULE'),
  ('html.entities', 'C:\\Software\\Python\\Lib\\html\\entities.py', 'PYMODULE'),
  ('html', 'C:\\Software\\Python\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('bs4.builder._htmlparser',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\bs4\\builder\\_htmlparser.py',
   'PYMODULE'),
  ('html.parser', 'C:\\Software\\Python\\Lib\\html\\parser.py', 'PYMODULE'),
  ('_markupbase', 'C:\\Software\\Python\\Lib\\_markupbase.py', 'PYMODULE'),
  ('bs4.builder',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\bs4\\builder\\__init__.py',
   'PYMODULE'),
  ('bs4.builder._lxml',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\bs4\\builder\\_lxml.py',
   'PYMODULE'),
  ('lxml',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\__init__.py',
   'PYMODULE'),
  ('lxml.usedoctest',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\usedoctest.py',
   'PYMODULE'),
  ('lxml.pyclasslookup',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\pyclasslookup.py',
   'PYMODULE'),
  ('lxml.isoschematron',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\isoschematron\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxslt',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\includes\\libxslt\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxml',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\includes\\libxml\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libexslt',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\includes\\libexslt\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.extlibs',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\includes\\extlibs\\__init__.py',
   'PYMODULE'),
  ('lxml.includes',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\includes\\__init__.py',
   'PYMODULE'),
  ('lxml.html.usedoctest',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\html\\usedoctest.py',
   'PYMODULE'),
  ('lxml.html.soupparser',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\html\\soupparser.py',
   'PYMODULE'),
  ('lxml.html.html5parser',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\html\\html5parser.py',
   'PYMODULE'),
  ('lxml.html.formfill',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\html\\formfill.py',
   'PYMODULE'),
  ('cgi', 'C:\\Software\\Python\\Lib\\cgi.py', 'PYMODULE'),
  ('difflib', 'C:\\Software\\Python\\Lib\\difflib.py', 'PYMODULE'),
  ('lxml.html.defs',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\html\\defs.py',
   'PYMODULE'),
  ('lxml.html.clean',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\html\\clean.py',
   'PYMODULE'),
  ('lxml.html.builder',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\html\\builder.py',
   'PYMODULE'),
  ('lxml.html._setmixin',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\html\\_setmixin.py',
   'PYMODULE'),
  ('lxml.html._html5builder',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\html\\_html5builder.py',
   'PYMODULE'),
  ('lxml.html._diffcommand',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\html\\_diffcommand.py',
   'PYMODULE'),
  ('optparse', 'C:\\Software\\Python\\Lib\\optparse.py', 'PYMODULE'),
  ('lxml.html.ElementSoup',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\html\\ElementSoup.py',
   'PYMODULE'),
  ('lxml.html',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\html\\__init__.py',
   'PYMODULE'),
  ('webbrowser', 'C:\\Software\\Python\\Lib\\webbrowser.py', 'PYMODULE'),
  ('glob', 'C:\\Software\\Python\\Lib\\glob.py', 'PYMODULE'),
  ('lxml.doctestcompare',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\doctestcompare.py',
   'PYMODULE'),
  ('doctest', 'C:\\Software\\Python\\Lib\\doctest.py', 'PYMODULE'),
  ('unittest', 'C:\\Software\\Python\\Lib\\unittest\\__init__.py', 'PYMODULE'),
  ('unittest.async_case',
   'C:\\Software\\Python\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.signals',
   'C:\\Software\\Python\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.main', 'C:\\Software\\Python\\Lib\\unittest\\main.py', 'PYMODULE'),
  ('unittest.runner',
   'C:\\Software\\Python\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.loader',
   'C:\\Software\\Python\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.suite',
   'C:\\Software\\Python\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.case', 'C:\\Software\\Python\\Lib\\unittest\\case.py', 'PYMODULE'),
  ('unittest._log', 'C:\\Software\\Python\\Lib\\unittest\\_log.py', 'PYMODULE'),
  ('unittest.result',
   'C:\\Software\\Python\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.util', 'C:\\Software\\Python\\Lib\\unittest\\util.py', 'PYMODULE'),
  ('pdb', 'C:\\Software\\Python\\Lib\\pdb.py', 'PYMODULE'),
  ('pydoc', 'C:\\Software\\Python\\Lib\\pydoc.py', 'PYMODULE'),
  ('http.server', 'C:\\Software\\Python\\Lib\\http\\server.py', 'PYMODULE'),
  ('socketserver', 'C:\\Software\\Python\\Lib\\socketserver.py', 'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Software\\Python\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data',
   'C:\\Software\\Python\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('tty', 'C:\\Software\\Python\\Lib\\tty.py', 'PYMODULE'),
  ('sysconfig', 'C:\\Software\\Python\\Lib\\sysconfig.py', 'PYMODULE'),
  ('_aix_support', 'C:\\Software\\Python\\Lib\\_aix_support.py', 'PYMODULE'),
  ('_bootsubprocess',
   'C:\\Software\\Python\\Lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('platform', 'C:\\Software\\Python\\Lib\\platform.py', 'PYMODULE'),
  ('code', 'C:\\Software\\Python\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'C:\\Software\\Python\\Lib\\codeop.py', 'PYMODULE'),
  ('bdb', 'C:\\Software\\Python\\Lib\\bdb.py', 'PYMODULE'),
  ('cmd', 'C:\\Software\\Python\\Lib\\cmd.py', 'PYMODULE'),
  ('lxml.cssselect',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\cssselect.py',
   'PYMODULE'),
  ('lxml.ElementInclude',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\ElementInclude.py',
   'PYMODULE'),
  ('bs4.builder._html5lib',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\bs4\\builder\\_html5lib.py',
   'PYMODULE'),
  ('fake_useragent',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\fake_useragent\\__init__.py',
   'PYMODULE'),
  ('fake_useragent.get_version',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\fake_useragent\\get_version.py',
   'PYMODULE'),
  ('fake_useragent.fake',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\fake_useragent\\fake.py',
   'PYMODULE'),
  ('fake_useragent.utils',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\fake_useragent\\utils.py',
   'PYMODULE'),
  ('fake_useragent.log',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\fake_useragent\\log.py',
   'PYMODULE'),
  ('fake_useragent.errors',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\fake_useragent\\errors.py',
   'PYMODULE'),
  ('scheduler', 'D:\\ai_test\\weibo\\scheduler.py', 'PYMODULE'),
  ('checkin_manager', 'D:\\ai_test\\weibo\\checkin_manager.py', 'PYMODULE'),
  ('weibo_client', 'D:\\ai_test\\weibo\\weibo_client.py', 'PYMODULE'),
  ('config', 'D:\\ai_test\\weibo\\config.py', 'PYMODULE'),
  ('user_manager', 'D:\\ai_test\\weibo\\user_manager.py', 'PYMODULE'),
  ('crypto_utils', 'D:\\ai_test\\weibo\\crypto_utils.py', 'PYMODULE'),
  ('cryptography.hazmat.primitives.kdf.pbkdf2',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\kdf\\pbkdf2.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.kdf',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\kdf\\__init__.py',
   'PYMODULE'),
  ('cryptography.fernet',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\fernet.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hmac',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\hmac.py',
   'PYMODULE'),
  ('typing', 'C:\\Software\\Python\\Lib\\typing.py', 'PYMODULE'),
  ('json', 'C:\\Software\\Python\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder', 'C:\\Software\\Python\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.decoder', 'C:\\Software\\Python\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.scanner', 'C:\\Software\\Python\\Lib\\json\\scanner.py', 'PYMODULE'),
  ('argparse', 'C:\\Software\\Python\\Lib\\argparse.py', 'PYMODULE')],
 [('selenium\\webdriver\\common\\windows\\selenium-manager.exe',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\selenium\\webdriver\\common\\windows\\selenium-manager.exe',
   'BINARY'),
  ('python311.dll', 'C:\\Software\\Python\\python311.dll', 'BINARY'),
  ('select.pyd', 'C:\\Software\\Python\\DLLs\\select.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Software\\Python\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd', 'C:\\Software\\Python\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'C:\\Software\\Python\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'C:\\Software\\Python\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Software\\Python\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_decimal.pyd', 'C:\\Software\\Python\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_socket.pyd', 'C:\\Software\\Python\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'C:\\Software\\Python\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'C:\\Software\\Python\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'C:\\Software\\Python\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_queue.pyd', 'C:\\Software\\Python\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_cffi_backend.cp311-win_amd64.pyd',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\_cffi_backend.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Software\\Python\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd', 'C:\\Software\\Python\\DLLs\\_asyncio.pyd', 'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp311-win_amd64.pyd',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp311-win_amd64.pyd',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\charset_normalizer\\md.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\etree.cp311-win_amd64.pyd',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\etree.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\_elementpath.cp311-win_amd64.pyd',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\_elementpath.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\sax.cp311-win_amd64.pyd',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\sax.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\objectify.cp311-win_amd64.pyd',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\objectify.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\html\\diff.cp311-win_amd64.pyd',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\html\\diff.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\builder.cp311-win_amd64.pyd',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\builder.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll', 'C:\\Software\\Python\\VCRUNTIME140.dll', 'BINARY'),
  ('libcrypto-1_1.dll',
   'C:\\Software\\Python\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('libssl-1_1.dll', 'C:\\Software\\Python\\DLLs\\libssl-1_1.dll', 'BINARY'),
  ('libffi-8.dll', 'C:\\Software\\Python\\DLLs\\libffi-8.dll', 'BINARY'),
  ('python3.dll', 'C:\\Software\\Python\\python3.dll', 'BINARY')],
 [],
 [],
 [('config_template.json', 'D:\\ai_test\\weibo\\config_template.json', 'DATA'),
  ('cryptography-45.0.4.dist-info\\INSTALLER',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography-45.0.4.dist-info\\INSTALLER',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\REQUESTED',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography-45.0.4.dist-info\\REQUESTED',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\licenses\\LICENSE.BSD',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography-45.0.4.dist-info\\licenses\\LICENSE.BSD',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\WHEEL',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography-45.0.4.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\licenses\\LICENSE',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography-45.0.4.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\RECORD',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography-45.0.4.dist-info\\RECORD',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\METADATA',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography-45.0.4.dist-info\\METADATA',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\licenses\\LICENSE.APACHE',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography-45.0.4.dist-info\\licenses\\LICENSE.APACHE',
   'DATA'),
  ('selenium\\webdriver\\common\\devtools\\v137\\py.typed',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\selenium\\webdriver\\common\\devtools\\v137\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\common\\devtools\\v135\\py.typed',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\selenium\\webdriver\\common\\devtools\\v135\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\common\\devtools\\v136\\py.typed',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\selenium\\webdriver\\common\\devtools\\v136\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\common\\macos\\selenium-manager',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\selenium\\webdriver\\common\\macos\\selenium-manager',
   'DATA'),
  ('selenium\\webdriver\\remote\\getAttribute.js',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\selenium\\webdriver\\remote\\getAttribute.js',
   'DATA'),
  ('selenium\\py.typed',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\selenium\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\firefox\\webdriver_prefs.json',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\selenium\\webdriver\\firefox\\webdriver_prefs.json',
   'DATA'),
  ('selenium\\webdriver\\common\\mutation-listener.js',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\selenium\\webdriver\\common\\mutation-listener.js',
   'DATA'),
  ('selenium\\webdriver\\remote\\findElements.js',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\selenium\\webdriver\\remote\\findElements.js',
   'DATA'),
  ('selenium\\webdriver\\common\\linux\\selenium-manager',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\selenium\\webdriver\\common\\linux\\selenium-manager',
   'DATA'),
  ('selenium\\webdriver\\remote\\isDisplayed.js',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\selenium\\webdriver\\remote\\isDisplayed.js',
   'DATA'),
  ('certifi\\py.typed',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('certifi\\cacert.pem',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'DATA'),
  ('fake_useragent-2.2.0.dist-info\\licenses\\LICENSE',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\fake_useragent-2.2.0.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('fake_useragent-2.2.0.dist-info\\licenses\\AUTHORS',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\fake_useragent-2.2.0.dist-info\\licenses\\AUTHORS',
   'DATA'),
  ('fake_useragent-2.2.0.dist-info\\RECORD',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\fake_useragent-2.2.0.dist-info\\RECORD',
   'DATA'),
  ('fake_useragent-2.2.0.dist-info\\INSTALLER',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\fake_useragent-2.2.0.dist-info\\INSTALLER',
   'DATA'),
  ('fake_useragent-2.2.0.dist-info\\WHEEL',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\fake_useragent-2.2.0.dist-info\\WHEEL',
   'DATA'),
  ('fake_useragent-2.2.0.dist-info\\top_level.txt',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\fake_useragent-2.2.0.dist-info\\top_level.txt',
   'DATA'),
  ('fake_useragent-2.2.0.dist-info\\REQUESTED',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\fake_useragent-2.2.0.dist-info\\REQUESTED',
   'DATA'),
  ('fake_useragent-2.2.0.dist-info\\METADATA',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\fake_useragent-2.2.0.dist-info\\METADATA',
   'DATA'),
  ('base_library.zip',
   'D:\\ai_test\\weibo\\build\\微博超话自动签到\\base_library.zip',
   'DATA')],
 [('abc', 'C:\\Software\\Python\\Lib\\abc.py', 'PYMODULE'),
  ('re._parser', 'C:\\Software\\Python\\Lib\\re\\_parser.py', 'PYMODULE'),
  ('re._constants', 'C:\\Software\\Python\\Lib\\re\\_constants.py', 'PYMODULE'),
  ('re._compiler', 'C:\\Software\\Python\\Lib\\re\\_compiler.py', 'PYMODULE'),
  ('re._casefix', 'C:\\Software\\Python\\Lib\\re\\_casefix.py', 'PYMODULE'),
  ('re', 'C:\\Software\\Python\\Lib\\re\\__init__.py', 'PYMODULE'),
  ('traceback', 'C:\\Software\\Python\\Lib\\traceback.py', 'PYMODULE'),
  ('ntpath', 'C:\\Software\\Python\\Lib\\ntpath.py', 'PYMODULE'),
  ('locale', 'C:\\Software\\Python\\Lib\\locale.py', 'PYMODULE'),
  ('copyreg', 'C:\\Software\\Python\\Lib\\copyreg.py', 'PYMODULE'),
  ('heapq', 'C:\\Software\\Python\\Lib\\heapq.py', 'PYMODULE'),
  ('os', 'C:\\Software\\Python\\Lib\\os.py', 'PYMODULE'),
  ('enum', 'C:\\Software\\Python\\Lib\\enum.py', 'PYMODULE'),
  ('keyword', 'C:\\Software\\Python\\Lib\\keyword.py', 'PYMODULE'),
  ('collections.abc',
   'C:\\Software\\Python\\Lib\\collections\\abc.py',
   'PYMODULE'),
  ('collections',
   'C:\\Software\\Python\\Lib\\collections\\__init__.py',
   'PYMODULE'),
  ('codecs', 'C:\\Software\\Python\\Lib\\codecs.py', 'PYMODULE'),
  ('operator', 'C:\\Software\\Python\\Lib\\operator.py', 'PYMODULE'),
  ('types', 'C:\\Software\\Python\\Lib\\types.py', 'PYMODULE'),
  ('sre_constants', 'C:\\Software\\Python\\Lib\\sre_constants.py', 'PYMODULE'),
  ('warnings', 'C:\\Software\\Python\\Lib\\warnings.py', 'PYMODULE'),
  ('sre_compile', 'C:\\Software\\Python\\Lib\\sre_compile.py', 'PYMODULE'),
  ('encodings.zlib_codec',
   'C:\\Software\\Python\\Lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   'C:\\Software\\Python\\Lib\\encodings\\uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   'C:\\Software\\Python\\Lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8',
   'C:\\Software\\Python\\Lib\\encodings\\utf_8.py',
   'PYMODULE'),
  ('encodings.utf_7',
   'C:\\Software\\Python\\Lib\\encodings\\utf_7.py',
   'PYMODULE'),
  ('encodings.utf_32_le',
   'C:\\Software\\Python\\Lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'C:\\Software\\Python\\Lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32',
   'C:\\Software\\Python\\Lib\\encodings\\utf_32.py',
   'PYMODULE'),
  ('encodings.utf_16_le',
   'C:\\Software\\Python\\Lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'C:\\Software\\Python\\Lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16',
   'C:\\Software\\Python\\Lib\\encodings\\utf_16.py',
   'PYMODULE'),
  ('encodings.unicode_escape',
   'C:\\Software\\Python\\Lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'C:\\Software\\Python\\Lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   'C:\\Software\\Python\\Lib\\encodings\\tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   'C:\\Software\\Python\\Lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'C:\\Software\\Python\\Lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'C:\\Software\\Python\\Lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13',
   'C:\\Software\\Python\\Lib\\encodings\\rot_13.py',
   'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'C:\\Software\\Python\\Lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'C:\\Software\\Python\\Lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   'C:\\Software\\Python\\Lib\\encodings\\punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   'C:\\Software\\Python\\Lib\\encodings\\ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos',
   'C:\\Software\\Python\\Lib\\encodings\\palmos.py',
   'PYMODULE'),
  ('encodings.oem', 'C:\\Software\\Python\\Lib\\encodings\\oem.py', 'PYMODULE'),
  ('encodings.mbcs',
   'C:\\Software\\Python\\Lib\\encodings\\mbcs.py',
   'PYMODULE'),
  ('encodings.mac_turkish',
   'C:\\Software\\Python\\Lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'C:\\Software\\Python\\Lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'C:\\Software\\Python\\Lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'C:\\Software\\Python\\Lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'C:\\Software\\Python\\Lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'C:\\Software\\Python\\Lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'C:\\Software\\Python\\Lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'C:\\Software\\Python\\Lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'C:\\Software\\Python\\Lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'C:\\Software\\Python\\Lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   'C:\\Software\\Python\\Lib\\encodings\\latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048',
   'C:\\Software\\Python\\Lib\\encodings\\kz1048.py',
   'PYMODULE'),
  ('encodings.koi8_u',
   'C:\\Software\\Python\\Lib\\encodings\\koi8_u.py',
   'PYMODULE'),
  ('encodings.koi8_t',
   'C:\\Software\\Python\\Lib\\encodings\\koi8_t.py',
   'PYMODULE'),
  ('encodings.koi8_r',
   'C:\\Software\\Python\\Lib\\encodings\\koi8_r.py',
   'PYMODULE'),
  ('encodings.johab',
   'C:\\Software\\Python\\Lib\\encodings\\johab.py',
   'PYMODULE'),
  ('encodings.iso8859_9',
   'C:\\Software\\Python\\Lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'C:\\Software\\Python\\Lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'C:\\Software\\Python\\Lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'C:\\Software\\Python\\Lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'C:\\Software\\Python\\Lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'C:\\Software\\Python\\Lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'C:\\Software\\Python\\Lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'C:\\Software\\Python\\Lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'C:\\Software\\Python\\Lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'C:\\Software\\Python\\Lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'C:\\Software\\Python\\Lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'C:\\Software\\Python\\Lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'C:\\Software\\Python\\Lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'C:\\Software\\Python\\Lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'C:\\Software\\Python\\Lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'C:\\Software\\Python\\Lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'C:\\Software\\Python\\Lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'C:\\Software\\Python\\Lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'C:\\Software\\Python\\Lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'C:\\Software\\Python\\Lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'C:\\Software\\Python\\Lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'C:\\Software\\Python\\Lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna',
   'C:\\Software\\Python\\Lib\\encodings\\idna.py',
   'PYMODULE'),
  ('encodings.hz', 'C:\\Software\\Python\\Lib\\encodings\\hz.py', 'PYMODULE'),
  ('encodings.hp_roman8',
   'C:\\Software\\Python\\Lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'C:\\Software\\Python\\Lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk', 'C:\\Software\\Python\\Lib\\encodings\\gbk.py', 'PYMODULE'),
  ('encodings.gb2312',
   'C:\\Software\\Python\\Lib\\encodings\\gb2312.py',
   'PYMODULE'),
  ('encodings.gb18030',
   'C:\\Software\\Python\\Lib\\encodings\\gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr',
   'C:\\Software\\Python\\Lib\\encodings\\euc_kr.py',
   'PYMODULE'),
  ('encodings.euc_jp',
   'C:\\Software\\Python\\Lib\\encodings\\euc_jp.py',
   'PYMODULE'),
  ('encodings.euc_jisx0213',
   'C:\\Software\\Python\\Lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'C:\\Software\\Python\\Lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950',
   'C:\\Software\\Python\\Lib\\encodings\\cp950.py',
   'PYMODULE'),
  ('encodings.cp949',
   'C:\\Software\\Python\\Lib\\encodings\\cp949.py',
   'PYMODULE'),
  ('encodings.cp932',
   'C:\\Software\\Python\\Lib\\encodings\\cp932.py',
   'PYMODULE'),
  ('encodings.cp875',
   'C:\\Software\\Python\\Lib\\encodings\\cp875.py',
   'PYMODULE'),
  ('encodings.cp874',
   'C:\\Software\\Python\\Lib\\encodings\\cp874.py',
   'PYMODULE'),
  ('encodings.cp869',
   'C:\\Software\\Python\\Lib\\encodings\\cp869.py',
   'PYMODULE'),
  ('encodings.cp866',
   'C:\\Software\\Python\\Lib\\encodings\\cp866.py',
   'PYMODULE'),
  ('encodings.cp865',
   'C:\\Software\\Python\\Lib\\encodings\\cp865.py',
   'PYMODULE'),
  ('encodings.cp864',
   'C:\\Software\\Python\\Lib\\encodings\\cp864.py',
   'PYMODULE'),
  ('encodings.cp863',
   'C:\\Software\\Python\\Lib\\encodings\\cp863.py',
   'PYMODULE'),
  ('encodings.cp862',
   'C:\\Software\\Python\\Lib\\encodings\\cp862.py',
   'PYMODULE'),
  ('encodings.cp861',
   'C:\\Software\\Python\\Lib\\encodings\\cp861.py',
   'PYMODULE'),
  ('encodings.cp860',
   'C:\\Software\\Python\\Lib\\encodings\\cp860.py',
   'PYMODULE'),
  ('encodings.cp858',
   'C:\\Software\\Python\\Lib\\encodings\\cp858.py',
   'PYMODULE'),
  ('encodings.cp857',
   'C:\\Software\\Python\\Lib\\encodings\\cp857.py',
   'PYMODULE'),
  ('encodings.cp856',
   'C:\\Software\\Python\\Lib\\encodings\\cp856.py',
   'PYMODULE'),
  ('encodings.cp855',
   'C:\\Software\\Python\\Lib\\encodings\\cp855.py',
   'PYMODULE'),
  ('encodings.cp852',
   'C:\\Software\\Python\\Lib\\encodings\\cp852.py',
   'PYMODULE'),
  ('encodings.cp850',
   'C:\\Software\\Python\\Lib\\encodings\\cp850.py',
   'PYMODULE'),
  ('encodings.cp775',
   'C:\\Software\\Python\\Lib\\encodings\\cp775.py',
   'PYMODULE'),
  ('encodings.cp737',
   'C:\\Software\\Python\\Lib\\encodings\\cp737.py',
   'PYMODULE'),
  ('encodings.cp720',
   'C:\\Software\\Python\\Lib\\encodings\\cp720.py',
   'PYMODULE'),
  ('encodings.cp500',
   'C:\\Software\\Python\\Lib\\encodings\\cp500.py',
   'PYMODULE'),
  ('encodings.cp437',
   'C:\\Software\\Python\\Lib\\encodings\\cp437.py',
   'PYMODULE'),
  ('encodings.cp424',
   'C:\\Software\\Python\\Lib\\encodings\\cp424.py',
   'PYMODULE'),
  ('encodings.cp273',
   'C:\\Software\\Python\\Lib\\encodings\\cp273.py',
   'PYMODULE'),
  ('encodings.cp1258',
   'C:\\Software\\Python\\Lib\\encodings\\cp1258.py',
   'PYMODULE'),
  ('encodings.cp1257',
   'C:\\Software\\Python\\Lib\\encodings\\cp1257.py',
   'PYMODULE'),
  ('encodings.cp1256',
   'C:\\Software\\Python\\Lib\\encodings\\cp1256.py',
   'PYMODULE'),
  ('encodings.cp1255',
   'C:\\Software\\Python\\Lib\\encodings\\cp1255.py',
   'PYMODULE'),
  ('encodings.cp1254',
   'C:\\Software\\Python\\Lib\\encodings\\cp1254.py',
   'PYMODULE'),
  ('encodings.cp1253',
   'C:\\Software\\Python\\Lib\\encodings\\cp1253.py',
   'PYMODULE'),
  ('encodings.cp1252',
   'C:\\Software\\Python\\Lib\\encodings\\cp1252.py',
   'PYMODULE'),
  ('encodings.cp1251',
   'C:\\Software\\Python\\Lib\\encodings\\cp1251.py',
   'PYMODULE'),
  ('encodings.cp1250',
   'C:\\Software\\Python\\Lib\\encodings\\cp1250.py',
   'PYMODULE'),
  ('encodings.cp1140',
   'C:\\Software\\Python\\Lib\\encodings\\cp1140.py',
   'PYMODULE'),
  ('encodings.cp1125',
   'C:\\Software\\Python\\Lib\\encodings\\cp1125.py',
   'PYMODULE'),
  ('encodings.cp1026',
   'C:\\Software\\Python\\Lib\\encodings\\cp1026.py',
   'PYMODULE'),
  ('encodings.cp1006',
   'C:\\Software\\Python\\Lib\\encodings\\cp1006.py',
   'PYMODULE'),
  ('encodings.cp037',
   'C:\\Software\\Python\\Lib\\encodings\\cp037.py',
   'PYMODULE'),
  ('encodings.charmap',
   'C:\\Software\\Python\\Lib\\encodings\\charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   'C:\\Software\\Python\\Lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'C:\\Software\\Python\\Lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5',
   'C:\\Software\\Python\\Lib\\encodings\\big5.py',
   'PYMODULE'),
  ('encodings.base64_codec',
   'C:\\Software\\Python\\Lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii',
   'C:\\Software\\Python\\Lib\\encodings\\ascii.py',
   'PYMODULE'),
  ('encodings.aliases',
   'C:\\Software\\Python\\Lib\\encodings\\aliases.py',
   'PYMODULE'),
  ('encodings',
   'C:\\Software\\Python\\Lib\\encodings\\__init__.py',
   'PYMODULE'),
  ('_collections_abc',
   'C:\\Software\\Python\\Lib\\_collections_abc.py',
   'PYMODULE'),
  ('weakref', 'C:\\Software\\Python\\Lib\\weakref.py', 'PYMODULE'),
  ('functools', 'C:\\Software\\Python\\Lib\\functools.py', 'PYMODULE'),
  ('genericpath', 'C:\\Software\\Python\\Lib\\genericpath.py', 'PYMODULE'),
  ('reprlib', 'C:\\Software\\Python\\Lib\\reprlib.py', 'PYMODULE'),
  ('stat', 'C:\\Software\\Python\\Lib\\stat.py', 'PYMODULE'),
  ('linecache', 'C:\\Software\\Python\\Lib\\linecache.py', 'PYMODULE'),
  ('_weakrefset', 'C:\\Software\\Python\\Lib\\_weakrefset.py', 'PYMODULE'),
  ('posixpath', 'C:\\Software\\Python\\Lib\\posixpath.py', 'PYMODULE'),
  ('io', 'C:\\Software\\Python\\Lib\\io.py', 'PYMODULE'),
  ('sre_parse', 'C:\\Software\\Python\\Lib\\sre_parse.py', 'PYMODULE')])
