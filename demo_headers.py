"""
演示Session中请求头的各种修改方法
"""
import requests
from typing import Dict


class HeaderDemo:
    """请求头演示类"""
    
    def __init__(self):
        self.session = requests.Session()
        
        # 设置默认请求头
        self.session.headers.update({
            'User-Agent': 'MyApp/1.0',
            'Accept': 'text/html,application/xhtml+xml',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Connection': 'keep-alive'
        })
        print("🔧 默认请求头已设置")
        self._print_current_headers()
    
    def _print_current_headers(self):
        """打印当前session的默认请求头"""
        print("📋 当前Session默认请求头:")
        for key, value in self.session.headers.items():
            print(f"   {key}: {value}")
        print()
    
    def demo_temporary_headers(self):
        """演示临时请求头"""
        print("🎯 演示1: 临时请求头（推荐方法）")
        
        # 临时请求头，不影响session默认设置
        temp_headers = {
            'Referer': 'https://example.com/page1',
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
        
        print("📤 发送请求时的完整请求头:")
        # 模拟请求（实际会合并session默认头和临时头）
        merged_headers = {**self.session.headers, **temp_headers}
        for key, value in merged_headers.items():
            print(f"   {key}: {value}")
        
        print("📋 请求后Session默认请求头（未改变）:")
        self._print_current_headers()
    
    def demo_modify_session_headers(self):
        """演示修改session默认请求头"""
        print("🎯 演示2: 修改Session默认请求头")
        
        # 修改session的默认请求头
        self.session.headers['Referer'] = 'https://example.com/permanent'
        self.session.headers['X-Custom-Header'] = 'PermanentValue'
        
        print("📋 修改后的Session默认请求头:")
        self._print_current_headers()
        
        # 后续所有请求都会携带这些头
        print("📤 后续请求都会自动携带这些请求头")
    
    def demo_override_headers(self):
        """演示请求头覆盖"""
        print("🎯 演示3: 请求头覆盖机制")
        
        # session中有默认的User-Agent
        print(f"📋 Session默认User-Agent: {self.session.headers.get('User-Agent')}")
        
        # 临时覆盖User-Agent
        override_headers = {
            'User-Agent': 'TemporaryAgent/2.0',
            'Referer': 'https://example.com/override'
        }
        
        print("📤 本次请求的User-Agent被临时覆盖:")
        merged_headers = {**self.session.headers, **override_headers}
        print(f"   User-Agent: {merged_headers.get('User-Agent')}")
        print(f"   Referer: {merged_headers.get('Referer')}")
        
        print(f"📋 请求后Session默认User-Agent恢复: {self.session.headers.get('User-Agent')}")
        print()
    
    def demo_conditional_headers(self):
        """演示条件性请求头"""
        print("🎯 演示4: 条件性请求头设置")
        
        def get_headers_for_request(request_type: str, target_url: str) -> Dict[str, str]:
            """根据请求类型和目标URL生成请求头"""
            headers = {}
            
            if request_type == 'api':
                headers.update({
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'Referer': 'https://example.com/app'
                })
            elif request_type == 'page':
                headers.update({
                    'Accept': 'text/html,application/xhtml+xml',
                    'Referer': 'https://example.com/home'
                })
            elif request_type == 'form':
                headers.update({
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'Referer': target_url.replace('/submit', '')
                })
            
            return headers
        
        # 不同类型的请求使用不同的请求头
        api_headers = get_headers_for_request('api', 'https://api.example.com/data')
        page_headers = get_headers_for_request('page', 'https://example.com/page')
        form_headers = get_headers_for_request('form', 'https://example.com/submit')
        
        print("📤 API请求头:")
        for key, value in api_headers.items():
            print(f"   {key}: {value}")
        
        print("📤 页面请求头:")
        for key, value in page_headers.items():
            print(f"   {key}: {value}")
        
        print("📤 表单请求头:")
        for key, value in form_headers.items():
            print(f"   {key}: {value}")
        print()
    
    def demo_smart_referer(self):
        """演示智能Referer管理"""
        print("🎯 演示5: 智能Referer管理")
        
        class SmartRefererManager:
            def __init__(self):
                self.last_page_url = "https://example.com"
                self.referer_rules = {
                    'api.example.com': 'https://example.com/app',
                    'example.com/login': 'https://example.com',
                    'example.com/profile': 'https://example.com/home'
                }
            
            def get_referer(self, target_url: str) -> str:
                """智能获取Referer"""
                for pattern, referer in self.referer_rules.items():
                    if pattern in target_url:
                        return referer
                return self.last_page_url
            
            def update_last_page(self, url: str):
                """更新最后访问的页面"""
                if not any(api in url for api in ['/api/', '/ajax/']):
                    self.last_page_url = url
        
        manager = SmartRefererManager()
        
        # 模拟不同请求的Referer
        urls = [
            'https://example.com/home',
            'https://api.example.com/data',
            'https://example.com/profile',
            'https://example.com/login'
        ]
        
        for url in urls:
            referer = manager.get_referer(url)
            print(f"📤 访问 {url}")
            print(f"   Referer: {referer}")
            manager.update_last_page(url)
            print(f"   更新last_page: {manager.last_page_url}")
            print()


def main():
    """主函数"""
    print("🎯 Session请求头管理演示")
    print("=" * 50)
    
    demo = HeaderDemo()
    
    demo.demo_temporary_headers()
    demo.demo_modify_session_headers()
    demo.demo_override_headers()
    demo.demo_conditional_headers()
    demo.demo_smart_referer()
    
    print("✅ 演示完成!")


if __name__ == "__main__":
    main()
