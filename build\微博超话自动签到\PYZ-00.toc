('D:\\ai_test\\weibo\\build\\微博超话自动签到\\PYZ-00.pyz',
 [('__future__', 'C:\\Software\\Python\\Lib\\__future__.py', 'PYMODULE'),
  ('_aix_support', 'C:\\Software\\Python\\Lib\\_aix_support.py', 'PYMODULE'),
  ('_bootsubprocess',
   'C:\\Software\\Python\\Lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Software\\Python\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression', 'C:\\Software\\Python\\Lib\\_compression.py', 'PYMODULE'),
  ('_markupbase', 'C:\\Software\\Python\\Lib\\_markupbase.py', 'PYMODULE'),
  ('_py_abc', 'C:\\Software\\Python\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydecimal', 'C:\\Software\\Python\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('_strptime', 'C:\\Software\\Python\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'C:\\Software\\Python\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse', 'C:\\Software\\Python\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'C:\\Software\\Python\\Lib\\ast.py', 'PYMODULE'),
  ('asyncio', 'C:\\Software\\Python\\Lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Software\\Python\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Software\\Python\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Software\\Python\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Software\\Python\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Software\\Python\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Software\\Python\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Software\\Python\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Software\\Python\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Software\\Python\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Software\\Python\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks', 'C:\\Software\\Python\\Lib\\asyncio\\locks.py', 'PYMODULE'),
  ('asyncio.log', 'C:\\Software\\Python\\Lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.mixins',
   'C:\\Software\\Python\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Software\\Python\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Software\\Python\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Software\\Python\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Software\\Python\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Software\\Python\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Software\\Python\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Software\\Python\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Software\\Python\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Software\\Python\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'C:\\Software\\Python\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks', 'C:\\Software\\Python\\Lib\\asyncio\\tasks.py', 'PYMODULE'),
  ('asyncio.threads',
   'C:\\Software\\Python\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'C:\\Software\\Python\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Software\\Python\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Software\\Python\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Software\\Python\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Software\\Python\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Software\\Python\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('base64', 'C:\\Software\\Python\\Lib\\base64.py', 'PYMODULE'),
  ('bdb', 'C:\\Software\\Python\\Lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'C:\\Software\\Python\\Lib\\bisect.py', 'PYMODULE'),
  ('bs4',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\bs4\\__init__.py',
   'PYMODULE'),
  ('bs4._deprecation',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\bs4\\_deprecation.py',
   'PYMODULE'),
  ('bs4._typing',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\bs4\\_typing.py',
   'PYMODULE'),
  ('bs4._warnings',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\bs4\\_warnings.py',
   'PYMODULE'),
  ('bs4.builder',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\bs4\\builder\\__init__.py',
   'PYMODULE'),
  ('bs4.builder._html5lib',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\bs4\\builder\\_html5lib.py',
   'PYMODULE'),
  ('bs4.builder._htmlparser',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\bs4\\builder\\_htmlparser.py',
   'PYMODULE'),
  ('bs4.builder._lxml',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\bs4\\builder\\_lxml.py',
   'PYMODULE'),
  ('bs4.css',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\bs4\\css.py',
   'PYMODULE'),
  ('bs4.dammit',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\bs4\\dammit.py',
   'PYMODULE'),
  ('bs4.element',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\bs4\\element.py',
   'PYMODULE'),
  ('bs4.exceptions',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\bs4\\exceptions.py',
   'PYMODULE'),
  ('bs4.filter',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\bs4\\filter.py',
   'PYMODULE'),
  ('bs4.formatter',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\bs4\\formatter.py',
   'PYMODULE'),
  ('bz2', 'C:\\Software\\Python\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'C:\\Software\\Python\\Lib\\calendar.py', 'PYMODULE'),
  ('certifi',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('cgi', 'C:\\Software\\Python\\Lib\\cgi.py', 'PYMODULE'),
  ('charset_normalizer',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('checkin_manager', 'D:\\ai_test\\weibo\\checkin_manager.py', 'PYMODULE'),
  ('cmd', 'C:\\Software\\Python\\Lib\\cmd.py', 'PYMODULE'),
  ('code', 'C:\\Software\\Python\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'C:\\Software\\Python\\Lib\\codeop.py', 'PYMODULE'),
  ('concurrent',
   'C:\\Software\\Python\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Software\\Python\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Software\\Python\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Software\\Python\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Software\\Python\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('config', 'D:\\ai_test\\weibo\\config.py', 'PYMODULE'),
  ('contextlib', 'C:\\Software\\Python\\Lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'C:\\Software\\Python\\Lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'C:\\Software\\Python\\Lib\\copy.py', 'PYMODULE'),
  ('crypto_utils', 'D:\\ai_test\\weibo\\crypto_utils.py', 'PYMODULE'),
  ('cryptography',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.fernet',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\fernet.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers.algorithms',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hmac',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\hmac.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.kdf',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\kdf\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.kdf.pbkdf2',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\kdf\\pbkdf2.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.padding',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.utils',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography.x509',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.x509.verification',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\cryptography\\x509\\verification.py',
   'PYMODULE'),
  ('csv', 'C:\\Software\\Python\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes', 'C:\\Software\\Python\\Lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._endian',
   'C:\\Software\\Python\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('dataclasses', 'C:\\Software\\Python\\Lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'C:\\Software\\Python\\Lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'C:\\Software\\Python\\Lib\\decimal.py', 'PYMODULE'),
  ('difflib', 'C:\\Software\\Python\\Lib\\difflib.py', 'PYMODULE'),
  ('dis', 'C:\\Software\\Python\\Lib\\dis.py', 'PYMODULE'),
  ('doctest', 'C:\\Software\\Python\\Lib\\doctest.py', 'PYMODULE'),
  ('email', 'C:\\Software\\Python\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'C:\\Software\\Python\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Software\\Python\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Software\\Python\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Software\\Python\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Software\\Python\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset', 'C:\\Software\\Python\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.contentmanager',
   'C:\\Software\\Python\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Software\\Python\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors', 'C:\\Software\\Python\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.feedparser',
   'C:\\Software\\Python\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Software\\Python\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header', 'C:\\Software\\Python\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.headerregistry',
   'C:\\Software\\Python\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Software\\Python\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message', 'C:\\Software\\Python\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.parser', 'C:\\Software\\Python\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.policy', 'C:\\Software\\Python\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.quoprimime',
   'C:\\Software\\Python\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils', 'C:\\Software\\Python\\Lib\\email\\utils.py', 'PYMODULE'),
  ('fake_useragent',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\fake_useragent\\__init__.py',
   'PYMODULE'),
  ('fake_useragent.errors',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\fake_useragent\\errors.py',
   'PYMODULE'),
  ('fake_useragent.fake',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\fake_useragent\\fake.py',
   'PYMODULE'),
  ('fake_useragent.get_version',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\fake_useragent\\get_version.py',
   'PYMODULE'),
  ('fake_useragent.log',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\fake_useragent\\log.py',
   'PYMODULE'),
  ('fake_useragent.utils',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\fake_useragent\\utils.py',
   'PYMODULE'),
  ('fnmatch', 'C:\\Software\\Python\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'C:\\Software\\Python\\Lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'C:\\Software\\Python\\Lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'C:\\Software\\Python\\Lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'C:\\Software\\Python\\Lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'C:\\Software\\Python\\Lib\\gettext.py', 'PYMODULE'),
  ('glob', 'C:\\Software\\Python\\Lib\\glob.py', 'PYMODULE'),
  ('gzip', 'C:\\Software\\Python\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'C:\\Software\\Python\\Lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'C:\\Software\\Python\\Lib\\hmac.py', 'PYMODULE'),
  ('html', 'C:\\Software\\Python\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities', 'C:\\Software\\Python\\Lib\\html\\entities.py', 'PYMODULE'),
  ('html.parser', 'C:\\Software\\Python\\Lib\\html\\parser.py', 'PYMODULE'),
  ('http', 'C:\\Software\\Python\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client', 'C:\\Software\\Python\\Lib\\http\\client.py', 'PYMODULE'),
  ('http.cookiejar',
   'C:\\Software\\Python\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.cookies', 'C:\\Software\\Python\\Lib\\http\\cookies.py', 'PYMODULE'),
  ('http.server', 'C:\\Software\\Python\\Lib\\http\\server.py', 'PYMODULE'),
  ('idna',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Software\\Python\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Software\\Python\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Software\\Python\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Software\\Python\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc', 'C:\\Software\\Python\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.machinery',
   'C:\\Software\\Python\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Software\\Python\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Software\\Python\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Software\\Python\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Software\\Python\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Software\\Python\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Software\\Python\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Software\\Python\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Software\\Python\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Software\\Python\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Software\\Python\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Software\\Python\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Software\\Python\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'C:\\Software\\Python\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Software\\Python\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Software\\Python\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Software\\Python\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'C:\\Software\\Python\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'C:\\Software\\Python\\Lib\\ipaddress.py', 'PYMODULE'),
  ('json', 'C:\\Software\\Python\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder', 'C:\\Software\\Python\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.encoder', 'C:\\Software\\Python\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.scanner', 'C:\\Software\\Python\\Lib\\json\\scanner.py', 'PYMODULE'),
  ('logging', 'C:\\Software\\Python\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('lxml',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\__init__.py',
   'PYMODULE'),
  ('lxml.ElementInclude',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\ElementInclude.py',
   'PYMODULE'),
  ('lxml.cssselect',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\cssselect.py',
   'PYMODULE'),
  ('lxml.doctestcompare',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\doctestcompare.py',
   'PYMODULE'),
  ('lxml.html',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\html\\__init__.py',
   'PYMODULE'),
  ('lxml.html.ElementSoup',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\html\\ElementSoup.py',
   'PYMODULE'),
  ('lxml.html._diffcommand',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\html\\_diffcommand.py',
   'PYMODULE'),
  ('lxml.html._html5builder',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\html\\_html5builder.py',
   'PYMODULE'),
  ('lxml.html._setmixin',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\html\\_setmixin.py',
   'PYMODULE'),
  ('lxml.html.builder',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\html\\builder.py',
   'PYMODULE'),
  ('lxml.html.clean',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\html\\clean.py',
   'PYMODULE'),
  ('lxml.html.defs',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\html\\defs.py',
   'PYMODULE'),
  ('lxml.html.formfill',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\html\\formfill.py',
   'PYMODULE'),
  ('lxml.html.html5parser',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\html\\html5parser.py',
   'PYMODULE'),
  ('lxml.html.soupparser',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\html\\soupparser.py',
   'PYMODULE'),
  ('lxml.html.usedoctest',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\html\\usedoctest.py',
   'PYMODULE'),
  ('lxml.includes',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\includes\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.extlibs',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\includes\\extlibs\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libexslt',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\includes\\libexslt\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxml',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\includes\\libxml\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxslt',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\includes\\libxslt\\__init__.py',
   'PYMODULE'),
  ('lxml.isoschematron',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\isoschematron\\__init__.py',
   'PYMODULE'),
  ('lxml.pyclasslookup',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\pyclasslookup.py',
   'PYMODULE'),
  ('lxml.usedoctest',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\lxml\\usedoctest.py',
   'PYMODULE'),
  ('lzma', 'C:\\Software\\Python\\Lib\\lzma.py', 'PYMODULE'),
  ('mimetypes', 'C:\\Software\\Python\\Lib\\mimetypes.py', 'PYMODULE'),
  ('multiprocessing',
   'C:\\Software\\Python\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Software\\Python\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Software\\Python\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Software\\Python\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Software\\Python\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Software\\Python\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Software\\Python\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Software\\Python\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Software\\Python\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Software\\Python\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Software\\Python\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Software\\Python\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Software\\Python\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Software\\Python\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Software\\Python\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Software\\Python\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Software\\Python\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Software\\Python\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Software\\Python\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Software\\Python\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Software\\Python\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Software\\Python\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Software\\Python\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'C:\\Software\\Python\\Lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'C:\\Software\\Python\\Lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'C:\\Software\\Python\\Lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'C:\\Software\\Python\\Lib\\opcode.py', 'PYMODULE'),
  ('optparse', 'C:\\Software\\Python\\Lib\\optparse.py', 'PYMODULE'),
  ('pathlib', 'C:\\Software\\Python\\Lib\\pathlib.py', 'PYMODULE'),
  ('pdb', 'C:\\Software\\Python\\Lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'C:\\Software\\Python\\Lib\\pickle.py', 'PYMODULE'),
  ('pkgutil', 'C:\\Software\\Python\\Lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'C:\\Software\\Python\\Lib\\platform.py', 'PYMODULE'),
  ('pprint', 'C:\\Software\\Python\\Lib\\pprint.py', 'PYMODULE'),
  ('py_compile', 'C:\\Software\\Python\\Lib\\py_compile.py', 'PYMODULE'),
  ('pydoc', 'C:\\Software\\Python\\Lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data',
   'C:\\Software\\Python\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Software\\Python\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('queue', 'C:\\Software\\Python\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'C:\\Software\\Python\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'C:\\Software\\Python\\Lib\\random.py', 'PYMODULE'),
  ('requests',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('runpy', 'C:\\Software\\Python\\Lib\\runpy.py', 'PYMODULE'),
  ('schedule',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\schedule\\__init__.py',
   'PYMODULE'),
  ('scheduler', 'D:\\ai_test\\weibo\\scheduler.py', 'PYMODULE'),
  ('secrets', 'C:\\Software\\Python\\Lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'C:\\Software\\Python\\Lib\\selectors.py', 'PYMODULE'),
  ('selenium',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\selenium\\__init__.py',
   'PYMODULE'),
  ('shlex', 'C:\\Software\\Python\\Lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'C:\\Software\\Python\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'C:\\Software\\Python\\Lib\\signal.py', 'PYMODULE'),
  ('socket', 'C:\\Software\\Python\\Lib\\socket.py', 'PYMODULE'),
  ('socketserver', 'C:\\Software\\Python\\Lib\\socketserver.py', 'PYMODULE'),
  ('socks', 'D:\\ai_test\\weibo\\Lib\\site-packages\\socks.py', 'PYMODULE'),
  ('soupsieve',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\soupsieve\\__init__.py',
   'PYMODULE'),
  ('soupsieve.__meta__',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\soupsieve\\__meta__.py',
   'PYMODULE'),
  ('soupsieve.css_match',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\soupsieve\\css_match.py',
   'PYMODULE'),
  ('soupsieve.css_parser',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\soupsieve\\css_parser.py',
   'PYMODULE'),
  ('soupsieve.css_types',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\soupsieve\\css_types.py',
   'PYMODULE'),
  ('soupsieve.pretty',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\soupsieve\\pretty.py',
   'PYMODULE'),
  ('soupsieve.util',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\soupsieve\\util.py',
   'PYMODULE'),
  ('ssl', 'C:\\Software\\Python\\Lib\\ssl.py', 'PYMODULE'),
  ('statistics', 'C:\\Software\\Python\\Lib\\statistics.py', 'PYMODULE'),
  ('string', 'C:\\Software\\Python\\Lib\\string.py', 'PYMODULE'),
  ('stringprep', 'C:\\Software\\Python\\Lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'C:\\Software\\Python\\Lib\\subprocess.py', 'PYMODULE'),
  ('sysconfig', 'C:\\Software\\Python\\Lib\\sysconfig.py', 'PYMODULE'),
  ('tarfile', 'C:\\Software\\Python\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'C:\\Software\\Python\\Lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'C:\\Software\\Python\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'C:\\Software\\Python\\Lib\\threading.py', 'PYMODULE'),
  ('token', 'C:\\Software\\Python\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'C:\\Software\\Python\\Lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc', 'C:\\Software\\Python\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('tty', 'C:\\Software\\Python\\Lib\\tty.py', 'PYMODULE'),
  ('typing', 'C:\\Software\\Python\\Lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('unittest', 'C:\\Software\\Python\\Lib\\unittest\\__init__.py', 'PYMODULE'),
  ('unittest._log', 'C:\\Software\\Python\\Lib\\unittest\\_log.py', 'PYMODULE'),
  ('unittest.async_case',
   'C:\\Software\\Python\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case', 'C:\\Software\\Python\\Lib\\unittest\\case.py', 'PYMODULE'),
  ('unittest.loader',
   'C:\\Software\\Python\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main', 'C:\\Software\\Python\\Lib\\unittest\\main.py', 'PYMODULE'),
  ('unittest.result',
   'C:\\Software\\Python\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'C:\\Software\\Python\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'C:\\Software\\Python\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'C:\\Software\\Python\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util', 'C:\\Software\\Python\\Lib\\unittest\\util.py', 'PYMODULE'),
  ('urllib', 'C:\\Software\\Python\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.error', 'C:\\Software\\Python\\Lib\\urllib\\error.py', 'PYMODULE'),
  ('urllib.parse', 'C:\\Software\\Python\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('urllib.request',
   'C:\\Software\\Python\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Software\\Python\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib3',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.http2',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'D:\\ai_test\\weibo\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('user_manager', 'D:\\ai_test\\weibo\\user_manager.py', 'PYMODULE'),
  ('utils', 'D:\\ai_test\\weibo\\utils.py', 'PYMODULE'),
  ('webbrowser', 'C:\\Software\\Python\\Lib\\webbrowser.py', 'PYMODULE'),
  ('weibo_client', 'D:\\ai_test\\weibo\\weibo_client.py', 'PYMODULE'),
  ('xml', 'C:\\Software\\Python\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.parsers',
   'C:\\Software\\Python\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Software\\Python\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax', 'C:\\Software\\Python\\Lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Software\\Python\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Software\\Python\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Software\\Python\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Software\\Python\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Software\\Python\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc', 'C:\\Software\\Python\\Lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xmlrpc.client', 'C:\\Software\\Python\\Lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('zipfile', 'C:\\Software\\Python\\Lib\\zipfile.py', 'PYMODULE'),
  ('zipimport', 'C:\\Software\\Python\\Lib\\zipimport.py', 'PYMODULE')])
