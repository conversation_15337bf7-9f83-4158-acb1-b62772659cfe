"""
加密工具模块
用于安全存储用户凭据
"""
import base64
import json
import os
from typing import Dict, Any
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC


class CryptoUtils:
    """加密工具类"""
    
    def __init__(self, password: str = None):
        """
        初始化加密工具
        
        Args:
            password: 加密密码，如果为None则使用默认密码
        """
        if password is None:
            # 使用默认密码（实际应用中应该让用户设置）
            password = "weibo_checkin_default_key_2024"
        
        self.password = password.encode()
        self._key = None
    
    def _get_key(self) -> bytes:
        """获取加密密钥"""
        if self._key is None:
            # 使用固定的salt（实际应用中应该随机生成并存储）
            salt = b'weibo_checkin_salt_2024'
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )
            key = base64.urlsafe_b64encode(kdf.derive(self.password))
            self._key = key
        return self._key
    
    def encrypt_data(self, data: Any) -> str:
        """
        加密数据
        
        Args:
            data: 要加密的数据
            
        Returns:
            加密后的base64字符串
        """
        try:
            # 将数据转换为JSON字符串
            json_str = json.dumps(data, ensure_ascii=False)
            json_bytes = json_str.encode('utf-8')
            
            # 加密
            f = Fernet(self._get_key())
            encrypted_data = f.encrypt(json_bytes)
            
            # 返回base64编码的字符串
            return base64.urlsafe_b64encode(encrypted_data).decode('utf-8')
            
        except Exception as e:
            raise Exception(f"数据加密失败: {e}")
    
    def decrypt_data(self, encrypted_str: str) -> Any:
        """
        解密数据
        
        Args:
            encrypted_str: 加密的base64字符串
            
        Returns:
            解密后的原始数据
        """
        try:
            # 解码base64
            encrypted_data = base64.urlsafe_b64decode(encrypted_str.encode('utf-8'))
            
            # 解密
            f = Fernet(self._get_key())
            decrypted_bytes = f.decrypt(encrypted_data)
            
            # 转换回原始数据
            json_str = decrypted_bytes.decode('utf-8')
            return json.loads(json_str)
            
        except Exception as e:
            raise Exception(f"数据解密失败: {e}")
    
    def encrypt_cookies(self, cookies: Dict) -> str:
        """
        加密cookies
        
        Args:
            cookies: cookies字典
            
        Returns:
            加密后的cookies字符串
        """
        return self.encrypt_data(cookies)
    
    def decrypt_cookies(self, encrypted_cookies: str) -> Dict:
        """
        解密cookies
        
        Args:
            encrypted_cookies: 加密的cookies字符串
            
        Returns:
            解密后的cookies字典
        """
        return self.decrypt_data(encrypted_cookies)


def create_crypto_utils(password: str = None) -> CryptoUtils:
    """
    创建加密工具实例
    
    Args:
        password: 加密密码
        
    Returns:
        CryptoUtils实例
    """
    return CryptoUtils(password)


def is_encryption_available() -> bool:
    """
    检查加密功能是否可用
    
    Returns:
        True如果加密功能可用，否则False
    """
    try:
        from cryptography.fernet import Fernet
        return True
    except ImportError:
        return False
