# 配置文件使用指南

## 📋 配置文件概述

程序提供了两个示例配置文件，用于不同的使用场景：

### 📄 文件说明

| 文件名 | 用途 | 适用场景 |
|--------|------|----------|
| `config.json.example` | 单用户模式配置模板 | 个人使用，管理单个微博账户 |
| `config_multi_user.json.example` | 多用户模式配置模板 | 管理多个微博账户 |

---

## 🎯 config.json.example (单用户模式)

### 📝 文件特点
- **简单配置**：适合个人用户
- **直接存储**：cookies 直接存储在配置文件中
- **快速上手**：配置简单，容易理解

### 🔧 主要配置项

#### 1. 用户配置
```json
{
  "user": {
    "cookies": {
      "SUB": "your_sub_cookie_here",
      "SUBP": "your_subp_cookie_here", 
      "SSOLoginState": "your_sso_login_state_here"
    }
  }
}
```
- **作用**：存储单个用户的登录凭据
- **使用**：将从浏览器获取的 cookies 填入对应字段

#### 2. 签到配置
```json
{
  "checkin": {
    "enabled_topics": [
      {
        "id": "1234567890123456",
        "name": "示例超话1",
        "enabled": true
      }
    ],
    "auto_discover": true,
    "max_topics": 50,
    "retry_times": 3,
    "delay_between_checkins": 2
  }
}
```
- **enabled_topics**：手动配置的超话列表
- **auto_discover**：是否自动发现新超话
- **max_topics**：最大超话数量限制
- **retry_times**：失败重试次数
- **delay_between_checkins**：超话间签到延迟（秒）

#### 3. 调度配置
```json
{
  "schedule": {
    "enabled": false,
    "time": "09:00",
    "timezone": "Asia/Shanghai"
  }
}
```
- **enabled**：是否启用定时任务
- **time**：每日执行时间（24小时制）
- **timezone**：时区设置

#### 4. 日志配置
```json
{
  "logging": {
    "level": "INFO",
    "file": "logs/weibo_checkin.log",
    "max_size": "10MB",
    "backup_count": 5
  }
}
```
- **level**：日志级别（DEBUG, INFO, WARNING, ERROR）
- **file**：日志文件路径
- **max_size**：单个日志文件最大大小
- **backup_count**：保留的日志文件数量

---

## 👥 config_multi_user.json.example (多用户模式)

### 📝 文件特点
- **多用户支持**：可管理多个微博账户
- **安全存储**：用户数据存储在独立的加密文件中
- **高级功能**：支持并发处理、独立日志等

### 🔧 主要配置项

#### 1. 多用户配置
```json
{
  "multi_user": {
    "enabled": true,
    "default_user": "user1",
    "concurrent_processing": false,
    "max_concurrent_users": 3,
    "rate_limit_delay": 5
  }
}
```
- **enabled**：启用多用户模式
- **default_user**：默认用户ID
- **concurrent_processing**：是否启用并发处理
- **max_concurrent_users**：最大并发用户数
- **rate_limit_delay**：用户间延迟时间（秒）

#### 2. 用户配置（简化）
```json
{
  "user": {
    "cookies": {}
  }
}
```
- **作用**：在多用户模式下，此配置为空
- **说明**：用户数据存储在 `users.json` 文件中

#### 3. 签到配置（简化）
```json
{
  "checkin": {
    "enabled_topics": [],
    "auto_discover": true,
    "max_topics": 50,
    "retry_times": 3,
    "delay_between_checkins": 2
  }
}
```
- **enabled_topics**：在多用户模式下为空数组
- **说明**：每个用户的超话列表独立管理

#### 4. 日志配置（增强）
```json
{
  "logging": {
    "level": "INFO",
    "file": "logs/weibo_checkin.log",
    "max_size": "10MB",
    "backup_count": 5,
    "separate_user_logs": true
  }
}
```
- **separate_user_logs**：为每个用户创建独立日志文件

---

## 🚀 使用方法

### 📋 步骤1：选择配置模式

#### 单用户模式
```bash
# 复制单用户配置模板
cp config.json.example config.json
```

#### 多用户模式
```bash
# 复制多用户配置模板
cp config_multi_user.json.example config.json
```

### 📋 步骤2：修改配置

#### 单用户模式配置
1. 编辑 `config.json`
2. 填入您的 cookies 信息
3. 根据需要调整其他参数

#### 多用户模式配置
1. 编辑 `config.json`（通常不需要修改）
2. 使用程序命令添加用户：
   ```bash
   python main.py --add-user user1 "用户名" "cookies_data"
   ```

### 📋 步骤3：验证配置
```bash
# 检查配置是否正确
python main.py --status
```

---

## ⚙️ 配置参数详解

### 🔧 通用参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `auto_discover` | boolean | true | 自动发现新超话 |
| `max_topics` | integer | 50 | 最大超话数量 |
| `retry_times` | integer | 3 | 失败重试次数 |
| `delay_between_checkins` | integer | 2 | 签到间隔（秒） |

### 🔧 多用户专用参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `concurrent_processing` | boolean | false | 并发处理（不推荐） |
| `max_concurrent_users` | integer | 3 | 最大并发数 |
| `rate_limit_delay` | integer | 5 | 用户间延迟 |
| `separate_user_logs` | boolean | true | 独立用户日志 |

### 🔧 日志级别说明

| 级别 | 说明 | 适用场景 |
|------|------|----------|
| `DEBUG` | 详细调试信息 | 开发调试 |
| `INFO` | 一般信息 | 日常使用 |
| `WARNING` | 警告信息 | 生产环境 |
| `ERROR` | 错误信息 | 问题排查 |

---

## 💡 最佳实践

### ✅ 推荐配置

#### 个人使用
- 使用 `config.json.example` 作为模板
- 设置 `auto_discover: true`
- 日志级别设为 `INFO`
- 启用定时任务

#### 多账户管理
- 使用 `config_multi_user.json.example` 作为模板
- 设置 `concurrent_processing: false`（更安全）
- 增加 `rate_limit_delay` 到 5-10 秒
- 启用 `separate_user_logs`

### ⚠️ 注意事项

1. **安全性**：
   - 不要将包含真实 cookies 的配置文件上传到公共仓库
   - 定期更新 cookies

2. **性能**：
   - 避免设置过小的延迟时间
   - 谨慎使用并发处理

3. **稳定性**：
   - 定期备份配置文件
   - 监控日志文件大小

---

## 🔄 配置迁移

### 从单用户迁移到多用户

1. **备份当前配置**：
   ```bash
   cp config.json config_single_user_backup.json
   ```

2. **切换到多用户模式**：
   ```bash
   cp config_multi_user.json.example config.json
   ```

3. **添加用户**：
   ```bash
   python main.py --add-user user1 "原用户" "原cookies"
   ```

4. **验证迁移**：
   ```bash
   python main.py --list-users
   ```

### 从多用户回退到单用户

1. **导出用户数据**：
   ```bash
   python export_topics.py
   ```

2. **切换配置**：
   ```bash
   cp config.json.example config.json
   ```

3. **手动配置用户信息**：
   编辑 `config.json`，填入用户 cookies 和超话信息

---

## 📞 故障排除

### 常见问题

1. **配置文件格式错误**：
   - 检查 JSON 语法是否正确
   - 使用在线 JSON 验证工具

2. **权限问题**：
   ```bash
   chmod 644 config.json
   ```

3. **配置不生效**：
   ```bash
   python main.py --reload-config
   ```

4. **重置配置**：
   ```bash
   cp config.json.example config.json
   # 或
   cp config_multi_user.json.example config.json
   ```

---

*最后更新：2025年6月10日*
