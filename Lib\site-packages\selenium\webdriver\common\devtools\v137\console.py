# DO NOT EDIT THIS FILE!
#
# This file is generated from the CDP specification. If you need to make
# changes, edit the generator and regenerate all of the modules.
#
# CDP domain: Console
from __future__ import annotations
from .util import event_class, T_JSON_DICT
from dataclasses import dataclass
import enum
import typing

@dataclass
class ConsoleMessage:
    '''
    Console message.
    '''
    #: Message source.
    source: str

    #: Message severity.
    level: str

    #: Message text.
    text: str

    #: URL of the message origin.
    url: typing.Optional[str] = None

    #: Line number in the resource that generated this message (1-based).
    line: typing.Optional[int] = None

    #: Column number in the resource that generated this message (1-based).
    column: typing.Optional[int] = None

    def to_json(self):
        json = dict()
        json['source'] = self.source
        json['level'] = self.level
        json['text'] = self.text
        if self.url is not None:
            json['url'] = self.url
        if self.line is not None:
            json['line'] = self.line
        if self.column is not None:
            json['column'] = self.column
        return json

    @classmethod
    def from_json(cls, json):
        return cls(
            source=str(json['source']),
            level=str(json['level']),
            text=str(json['text']),
            url=str(json['url']) if 'url' in json else None,
            line=int(json['line']) if 'line' in json else None,
            column=int(json['column']) if 'column' in json else None,
        )


def clear_messages() -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    Does nothing.
    '''
    cmd_dict: T_JSON_DICT = {
        'method': 'Console.clearMessages',
    }
    json = yield cmd_dict


def disable() -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    Disables console domain, prevents further console messages from being reported to the client.
    '''
    cmd_dict: T_JSON_DICT = {
        'method': 'Console.disable',
    }
    json = yield cmd_dict


def enable() -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    Enables console domain, sends the messages collected so far to the client by means of the
    ``messageAdded`` notification.
    '''
    cmd_dict: T_JSON_DICT = {
        'method': 'Console.enable',
    }
    json = yield cmd_dict


@event_class('Console.messageAdded')
@dataclass
class MessageAdded:
    '''
    Issued when new console message is added.
    '''
    #: Console message that has been added.
    message: ConsoleMessage

    @classmethod
    def from_json(cls, json: T_JSON_DICT) -> MessageAdded:
        return cls(
            message=ConsoleMessage.from_json(json['message'])
        )
