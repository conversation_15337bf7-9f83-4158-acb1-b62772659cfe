# 微博超话自动签到 v2.0.0 使用说明

## 🎯 快速开始

### 方法一：使用启动脚本（推荐新手）
1. 双击 `启动程序.bat` 
2. 按照菜单提示操作
3. 首次使用请先添加用户账户

### 方法二：直接运行程序
1. 双击 `微博超话自动签到.exe`
2. 使用命令行参数或交互模式

### 方法三：快速签到
1. 双击 `快速签到.bat`
2. 自动执行所有用户的签到任务

## 📋 主要功能

### 1. 用户管理
- ✅ 添加/删除用户账户
- ✅ 启用/禁用用户
- ✅ 更新用户cookies
- ✅ 查看用户列表和统计

### 2. 超话管理
- ✅ 自动发现用户关注的超话
- ✅ 手动添加/删除超话
- ✅ 查看超话列表

### 3. 签到功能
- ✅ 多用户批量签到
- ✅ 单用户签到
- ✅ 并发处理（可选）
- ✅ 定时任务

### 4. 系统功能
- ✅ 详细日志记录
- ✅ 配置管理
- ✅ 状态监控

## 🔧 常用命令

### 用户管理
```bash
# 添加用户
微博超话自动签到.exe --add-user USER_ID USERNAME COOKIES

# 列出用户
微博超话自动签到.exe --list-users

# 启用/禁用用户
微博超话自动签到.exe --enable-user USER_ID
微博超话自动签到.exe --disable-user USER_ID
```

### 签到操作
```bash
# 多用户签到
微博超话自动签到.exe --multi-checkin

# 单用户签到
微博超话自动签到.exe --user-checkin USER_ID

# 发现超话
微博超话自动签到.exe --discover-user USER_ID
```

### 系统操作
```bash
# 查看状态
微博超话自动签到.exe --status

# 交互模式
微博超话自动签到.exe --interactive

# 定时任务
微博超话自动签到.exe --start-scheduler
```

## 📁 文件说明

- `微博超话自动签到.exe` - 主程序
- `启动程序.bat` - 图形化启动脚本
- `快速签到.bat` - 一键签到脚本
- `config.json` - 主配置文件（自动生成）
- `users.json` - 用户数据文件（自动生成）
- `config_template.json` - 配置模板
- `logs/` - 日志文件目录
- `docs/` - 详细文档目录

## ⚠️ 注意事项

### 1. 首次使用
- 程序会自动创建配置文件
- 请先添加至少一个用户账户
- 建议先测试单用户签到

### 2. Cookies获取
- 登录微博网页版
- 按F12打开开发者工具
- 在Network标签页找到请求
- 复制Cookie字段的值

### 3. 安全建议
- 定期备份用户数据文件
- 保护好cookies信息
- 注意程序更新

### 4. 故障排除
- 查看logs目录下的日志文件
- 检查网络连接
- 确认cookies是否过期

## 📞 技术支持

如遇问题请：
1. 查看日志文件了解详细错误信息
2. 参考docs目录下的技术文档
3. 检查配置文件是否正确

## 📝 版本信息

- 版本: 2.0.0
- 构建时间: 2025-06-10 21:15:59
- 支持系统: Windows 7/10/11
- Python版本: 3.8+

## 🎉 更新日志

### v2.0.0
- ✅ 统一多用户架构
- ✅ 移除传统单用户模式
- ✅ 优化API解析逻辑
- ✅ 修复已知问题
- ✅ 改进用户体验

---

感谢使用 微博超话自动签到！
