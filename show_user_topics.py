#!/usr/bin/env python3
"""
查看用户超话信息的脚本
"""

from config import Config
from user_manager import UserManager

def show_user_topics():
    """显示用户的超话信息"""
    try:
        # 初始化配置
        config = Config()
        
        # 获取所有用户
        users = config.list_users()
        
        if not users:
            print("❌ 没有找到任何用户")
            return
        
        print(f"📋 用户超话信息 (共 {len(users)} 个用户):")
        print("=" * 60)
        
        for user in users:
            user_id = user['user_id']
            username = user['username']
            status = "✅ 启用" if user['enabled'] else "❌ 禁用"
            
            print(f"\n👤 用户: {username} (ID: {user_id}) - {status}")
            print(f"📊 统计: 签到 {user['total_checkins']} 次, 成功率 {user['success_rate']:.1f}%")
            
            # 获取用户详细信息
            user_data = config.get_user(user_id)
            if user_data:
                topics = user_data.get('enabled_topics', [])
                
                if topics:
                    print(f"📋 超话列表 ({len(topics)} 个):")
                    for i, topic in enumerate(topics, 1):
                        topic_status = "✅" if topic.get('enabled', True) else "❌"
                        topic_name = topic.get('name', '未知')
                        topic_id = topic.get('id', '未知')
                        print(f"   {i:2d}. {topic_status} {topic_name}")
                        print(f"       ID: {topic_id}")
                else:
                    print("📋 超话列表: 暂无")
                
                # 显示用户设置
                settings = user_data.get('settings', {})
                print(f"⚙️  设置:")
                print(f"   自动发现: {'✅' if settings.get('auto_discover', True) else '❌'}")
                print(f"   最大超话: {settings.get('max_topics', 50)} 个")
                print(f"   重试次数: {settings.get('retry_times', 3)} 次")
                print(f"   签到间隔: {settings.get('delay_between_checkins', 2)} 秒")
            else:
                print("❌ 无法获取用户详细信息")
            
            print("-" * 60)
    
    except Exception as e:
        print(f"❌ 查看用户超话信息失败: {e}")

if __name__ == "__main__":
    show_user_topics()
