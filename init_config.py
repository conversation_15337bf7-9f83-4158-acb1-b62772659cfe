#!/usr/bin/env python3
"""
配置文件初始化工具
自动生成和管理配置文件
"""

import os
import json
import shutil
from datetime import datetime

def check_config_files():
    """检查配置文件状态"""
    print("🔍 检查配置文件状态...")
    print("=" * 50)
    
    files_status = {
        "config.json": os.path.exists("config.json"),
        "config.json.example": os.path.exists("config.json.example"),
        "config_multi_user.json.example": os.path.exists("config_multi_user.json.example"),
        "users.json": os.path.exists("users.json")
    }
    
    for file_name, exists in files_status.items():
        status = "✅ 存在" if exists else "❌ 不存在"
        print(f"📁 {file_name:<30} {status}")
    
    return files_status

def create_default_config():
    """创建默认配置文件"""
    print("\n🚀 配置文件初始化向导")
    print("=" * 50)
    
    # 检查是否已有配置文件
    if os.path.exists("config.json"):
        print("⚠️  config.json 文件已存在")
        choice = input("是否要覆盖现有配置？(y/N): ").lower().strip()
        if choice != 'y':
            print("❌ 取消操作")
            return False
    
    # 选择配置模式
    print("\n📋 请选择配置模式:")
    print("1. 单用户模式 - 适合个人使用")
    print("2. 多用户模式 - 适合管理多个账户")
    
    while True:
        choice = input("\n请选择 (1/2): ").strip()
        if choice in ['1', '2']:
            break
        print("❌ 请输入 1 或 2")
    
    # 根据选择复制配置文件
    if choice == '1':
        return create_single_user_config()
    else:
        return create_multi_user_config()

def create_single_user_config():
    """创建单用户配置"""
    print("\n📝 创建单用户配置...")
    
    if not os.path.exists("config.json.example"):
        print("❌ config.json.example 文件不存在，创建默认配置")
        config = create_default_single_user_config()
    else:
        # 复制示例文件
        shutil.copy("config.json.example", "config.json")
        print("✅ 已复制 config.json.example 到 config.json")
        
        # 读取配置
        with open("config.json", 'r', encoding='utf-8') as f:
            config = json.load(f)
    
    # 询问是否要配置cookies
    print("\n🍪 是否现在配置用户cookies？")
    choice = input("输入 y 立即配置，或按回车跳过: ").lower().strip()
    
    if choice == 'y':
        print("\n请输入您的微博cookies:")
        print("💡 提示：从浏览器开发者工具中复制完整的Cookie字符串")
        cookies_str = input("Cookies: ").strip()
        
        if cookies_str:
            # 解析cookies字符串
            cookies = parse_cookies_string(cookies_str)
            if cookies:
                config["user"]["cookies"] = cookies
                print("✅ Cookies配置成功")
            else:
                print("⚠️  Cookies格式可能有误，请稍后手动配置")
    
    # 保存配置
    with open("config.json", 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)
    
    print("✅ 单用户配置创建完成")
    return True

def create_multi_user_config():
    """创建多用户配置"""
    print("\n📝 创建多用户配置...")
    
    if not os.path.exists("config_multi_user.json.example"):
        print("❌ config_multi_user.json.example 文件不存在，创建默认配置")
        config = create_default_multi_user_config()
    else:
        # 复制示例文件
        shutil.copy("config_multi_user.json.example", "config.json")
        print("✅ 已复制 config_multi_user.json.example 到 config.json")
        
        # 读取配置
        with open("config.json", 'r', encoding='utf-8') as f:
            config = json.load(f)
    
    # 保存配置
    with open("config.json", 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)
    
    print("✅ 多用户配置创建完成")
    print("💡 提示：使用以下命令添加用户:")
    print("   python main.py --add-user user1 \"用户名\" \"cookies\"")
    return True

def create_default_single_user_config():
    """创建默认单用户配置"""
    return {
        "user": {
            "cookies": {
                "SUB": "your_sub_cookie_here",
                "SUBP": "your_subp_cookie_here",
                "SSOLoginState": "your_sso_login_state_here"
            }
        },
        "checkin": {
            "enabled_topics": [],
            "auto_discover": True,
            "max_topics": 50,
            "retry_times": 3,
            "delay_between_checkins": 2
        },
        "schedule": {
            "enabled": False,
            "time": "09:00",
            "timezone": "Asia/Shanghai"
        },
        "logging": {
            "level": "INFO",
            "file": "logs/weibo_checkin.log",
            "max_size": "10MB",
            "backup_count": 5
        }
    }

def create_default_multi_user_config():
    """创建默认多用户配置"""
    return {
        "multi_user": {
            "enabled": True,
            "default_user": None,
            "concurrent_processing": False,
            "max_concurrent_users": 3,
            "rate_limit_delay": 5
        },
        "user": {
            "cookies": {}
        },
        "checkin": {
            "enabled_topics": [],
            "auto_discover": True,
            "max_topics": 50,
            "retry_times": 3,
            "delay_between_checkins": 2
        },
        "schedule": {
            "enabled": False,
            "time": "09:00",
            "timezone": "Asia/Shanghai"
        },
        "logging": {
            "level": "INFO",
            "file": "logs/weibo_checkin.log",
            "max_size": "10MB",
            "backup_count": 5,
            "separate_user_logs": True
        }
    }

def parse_cookies_string(cookies_str):
    """解析cookies字符串"""
    try:
        cookies = {}
        for item in cookies_str.split(';'):
            if '=' in item:
                key, value = item.strip().split('=', 1)
                cookies[key] = value
        
        # 检查必需的cookies
        required_cookies = ['SUB', 'SUBP']
        if any(key in cookies for key in required_cookies):
            return cookies
        else:
            return None
    except Exception:
        return None

def backup_existing_config():
    """备份现有配置"""
    if os.path.exists("config.json"):
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"config_backup_{timestamp}.json"
        shutil.copy("config.json", backup_name)
        print(f"✅ 已备份现有配置到: {backup_name}")
        return backup_name
    return None

def show_config_info():
    """显示配置信息"""
    if not os.path.exists("config.json"):
        print("❌ config.json 文件不存在")
        return
    
    try:
        with open("config.json", 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print("\n📋 当前配置信息:")
        print("=" * 30)
        
        # 检查模式
        multi_user_enabled = config.get("multi_user", {}).get("enabled", False)
        mode = "多用户模式" if multi_user_enabled else "单用户模式"
        print(f"🎯 运行模式: {mode}")
        
        # 显示基本配置
        checkin_config = config.get("checkin", {})
        print(f"🔄 自动发现: {'启用' if checkin_config.get('auto_discover', True) else '禁用'}")
        print(f"📊 最大超话: {checkin_config.get('max_topics', 50)} 个")
        print(f"🔁 重试次数: {checkin_config.get('retry_times', 3)} 次")
        
        # 显示调度配置
        schedule_config = config.get("schedule", {})
        schedule_enabled = schedule_config.get("enabled", False)
        print(f"⏰ 定时任务: {'启用' if schedule_enabled else '禁用'}")
        if schedule_enabled:
            print(f"   执行时间: {schedule_config.get('time', '09:00')}")
        
        # 显示日志配置
        logging_config = config.get("logging", {})
        print(f"📝 日志级别: {logging_config.get('level', 'INFO')}")
        print(f"📁 日志文件: {logging_config.get('file', 'logs/weibo_checkin.log')}")
        
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")

def main():
    """主函数"""
    print("🎯 微博超话自动签到程序 - 配置初始化工具")
    print("=" * 60)
    
    # 检查文件状态
    files_status = check_config_files()
    
    # 显示菜单
    print("\n📋 可用操作:")
    print("1. 创建配置文件")
    print("2. 显示当前配置信息")
    print("3. 备份现有配置")
    print("4. 重置配置文件")
    print("0. 退出")
    
    while True:
        choice = input("\n请选择操作 (0-4): ").strip()
        
        if choice == '0':
            print("👋 再见！")
            break
        elif choice == '1':
            create_default_config()
        elif choice == '2':
            show_config_info()
        elif choice == '3':
            backup_file = backup_existing_config()
            if backup_file:
                print(f"✅ 配置已备份到: {backup_file}")
            else:
                print("❌ 没有找到配置文件")
        elif choice == '4':
            backup_existing_config()
            create_default_config()
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()
